# eslint-plugin-simple-import-sort

Easy autofixable import sorting.

- ✅️ Runs via [`eslint --fix`][eslint-fix] – no new tooling
- ✅️ Also sorts exports where possible
- ✅️ <PERSON><PERSON> comments
- ✅️ Handles type imports/exports
- ✅️ [TypeScript] friendly \(via [@typescript-eslint/parser])
- ✅️ [Prettier] friendly
- ✅️ [dprint] friendly ([with configuration][dprint-configuration])
- ✅️ [eslint-plugin-import] friendly
- ✅️ `git diff` friendly
- ✅️ 100% code coverage
- ✅️ No dependencies
- ❌ [Does not support `require`][no-require]

This is for those who use [`eslint --fix`][eslint-fix] (autofix) a lot and want to completely forget about sorting imports!

[@typescript-eslint/parser]: https://github.com/typescript-eslint/typescript-eslint/tree/master/packages/parser
[dprint-configuration]: https://github.com/lydell/eslint-plugin-simple-import-sort/#how-do-i-use-this-with-dprint
[dprint]: https://dprint.dev/
[eslint-fix]: https://eslint.org/docs/user-guide/command-line-interface#--fix
[eslint-plugin-import]: https://github.com/import-js/eslint-plugin-import/
[no-require]: https://github.com/lydell/eslint-plugin-simple-import-sort/#does-it-support-require
[prettier]: https://prettier.io/
[typescript]: https://www.typescriptlang.org/

## Example

**[➡️ Full readme](https://github.com/lydell/eslint-plugin-simple-import-sort/)**