(()=>{"use strict";var e={4091:function(e,t,o){var n,i,r,s,l,a,d,c=o(5893),u=o(745),p=o(3444);o(3761);let h=o.p+"static/image/icon-start.8353312d.jpg";var g=o(7141),x=o(7294),m=o(2902),f=o(6493),y=o(4112),v=o(2248),j=o(4549),C=o(7973),b=o(5339);let w=async(e,t)=>{let o=e.get(v.sS),{document:n}=e,i=e.get(p.iwM),r=e.get(p.dmf),{fromPort:s,toPort:l,mousePos:a,line:d,originLine:c}=t;if(c||!d||l)return;let u=v.QV.getContainerNode({fromPort:s}),h=await o.singleSelectNodePanel({position:a,containerNode:u,panelProps:{enableNodePlaceholder:!0,enableScrollClose:!0}});if(!h)return;let{nodeType:g,nodeJSON:x}=h,m=v.QV.adjustNodePosition({nodeType:g,position:a,fromPort:s,toPort:l,containerNode:u,document:n,dragService:i}),f=n.createWorkflowNodeByType(g,m,x??{},null==u?void 0:u.id);await (0,p.gw0)(20),v.QV.buildLine({fromPort:s,node:f,toPort:l,linesManager:r})},L="flowgram-workflow-clipboard-data";var N=((n={}).COPY="COPY",n.PASTE="PASTE",n.CUT="CUT",n.GROUP="GROUP",n.UNGROUP="UNGROUP",n.COLLAPSE="COLLAPSE",n.EXPAND="EXPAND",n.DELETE="DELETE",n.ZOOM_IN="ZOOM_IN",n.ZOOM_OUT="ZOOM_OUT",n.RESET_ZOOM="RESET_ZOOM",n.SELECT_ALL="SELECT_ALL",n.CANCEL_SELECT="CANCEL_SELECT",n);class M{async execute(){this.playgroundConfig.zoom>1.9||this.playgroundConfig.zoomout()}constructor(e){this.commandId=N.ZOOM_OUT,this.shortcuts=["meta -","ctrl -"],this.playgroundConfig=e.get(p.ERL),this.execute=this.execute.bind(this)}}class k{async execute(){this.playgroundConfig.zoom>1.9||this.playgroundConfig.zoomin()}constructor(e){this.commandId=N.ZOOM_IN,this.shortcuts=["meta =","ctrl ="],this.playgroundConfig=e.get(p.ERL),this.execute=this.execute.bind(this)}}class S{async execute(){let e=this.document.getAllNodes();this.playground.selectionService.selection=e}constructor(e){this.commandId=N.SELECT_ALL,this.shortcuts=["meta a","ctrl a"],this.document=e.get(p.L4w),this.playground=e.playground,this.execute=this.execute.bind(this)}}var D=o(4922),z=o(3413);let I=(e,t)=>{let o=Array.isArray(t)?e=>{t.forEach(t=>t(e))}:t;return a.traverseNodes({value:e},o),e};!function(e){e.traverseNodes=(o,n)=>{let{value:i}=o;if(i){if("[object Object]"===Object.prototype.toString.call(i))Object.entries(i).forEach(t=>{let[r,s]=t;return e.traverseNodes({value:s,container:i,key:r,parent:o},n)});else if(Array.isArray(i))for(let t=i.length-1;t>=0;t--){let r=i[t];e.traverseNodes({value:r,container:i,index:t,parent:o},n)}n(t({node:o}))}};let t=e=>{let{node:t}=e;return{node:t,setValue:e=>o(t,e),getParents:()=>n(t),getPath:()=>i(t),getStringifyPath:()=>r(t),deleteSelf:()=>s(t)}},o=(e,t)=>{if(!t||!e)return;e.value=t;let{container:o,key:n,index:i}=e;n&&o?o[n]=t:"number"==typeof i&&(o[i]=t)},n=e=>{let t=[],o=e;for(;o;)t.unshift(o),o=o.parent;return t},i=e=>{let t=[];return n(e).forEach(e=>{e.key?t.unshift(e.key):e.index&&t.unshift(e.index)}),t},r=e=>i(e).reduce((e,t)=>"string"!=typeof t?`${e}[${t}]`:/\W/g.test(t)?`${e}["${t}"]`:`${e}.${t}`,""),s=e=>{let{container:t,key:o,index:n}=e;o&&t?delete t[o]:"number"==typeof n&&t.splice(n,1)}}(a||(a={})),function(e){let t=(0,z.kP)("1234567890",6);e.getAllNodeIds=e=>{let t=new Set,o=e=>{var n;t.add(e.id),(null==(n=e.blocks)?void 0:n.length)&&e.blocks.forEach(e=>o(e))};return e.nodes.forEach(e=>o(e)),Array.from(t)},e.generateNodeReplaceMap=(e,o)=>{let n=new Map;return e.forEach(e=>{if(o(e))n.set(e,e);else{let i;do i=t();while(!o(i));n.set(e,i)}}),n};let o=e=>null!=e,n=e=>{var t,n,i,r,s,l,a;let{node:d}=e;return!!((null==d?void 0:d.key)&&["sourceNodeID","targetNodeID"].includes(d.key)&&(null==(n=d.parent)||null==(t=n.parent)?void 0:t.key)==="edges"||(null==d?void 0:d.key)==="id"&&o(null==(i=d.container)?void 0:i.type)&&o(null==(r=d.container)?void 0:r.meta)&&o(null==(s=d.container)?void 0:s.data)||(null==d?void 0:d.key)==="blockID"&&o(null==(l=d.container)?void 0:l.name)&&(null==(a=d.container)?void 0:a.source)==="block-output")};e.replaceNodeId=(e,t)=>(I(e,e=>{if(!n(e))return;let{node:o}=e;t.has(o.value)&&e.setValue(t.get(o.value))}),e)}(d||(d={}));let E=e=>{let{json:t,isUniqueId:o}=e,n=d.getAllNodeIds(t),i=d.generateNodeReplaceMap(n,o);return d.replaceNodeId(t,i)};class T{async execute(){let e=await this.tryReadClipboard();if(!e||!this.isValidData(e))return;let t=this.apply(e);return t.length>0&&(D.FN.success({content:"Copy successfully",showClose:!1}),await this.nextTick(),this.scrollNodesToView(t)),t}apply(e){let{json:t}=e,o=E({json:t,isUniqueId:e=>!this.entityManager.getEntityById(e)}),n=this.calcPasteOffset(e.bounds),i=this.getSelectedContainer();this.applyOffset({json:o,offset:n,parent:i});let{nodes:r}=this.document.renderJSON(o,{parent:i});return this.selectNodes(r),r}isValidData(e){return(null==e?void 0:e.type)!==L?(D.FN.error({content:"Invalid clipboard data"}),!1):e.source.host===window.location.host||(D.FN.error({content:"Cannot paste nodes from different host"}),!1)}async tryReadClipboard(){try{let e=await navigator.clipboard.readText()||"";return JSON.parse(e)}catch(e){return}}calcPasteOffset(e){let{x:t,y:o,width:n,height:i}=e,{center:r}=new p.AeJ(t,o,n,i),s=this.hoverService.hoveredPos;return{x:s.x-r.x,y:s.y-r.y}}applyOffset(e){let{json:t,offset:o,parent:n}=e;t.nodes.forEach(e=>{var t;if(!(null==(t=e.meta)?void 0:t.position))return;let i={x:e.meta.position.x+o.x,y:e.meta.position.y+o.y};n&&(i=this.dragService.adjustSubNodePosition(e.type,n,i)),e.meta.position=i})}getSelectedContainer(){let{activatedNode:e}=this.selectService;return(null==e?void 0:e.getNodeMeta().isContainer)?e:void 0}selectNodes(e){this.selectService.selection=e}async scrollNodesToView(e){let t=e.map(e=>e.getData(p.eGM).bounds);await this.document.playgroundConfig.scrollToView({bounds:p.AeJ.enlarge(t)})}async nextTick(){await (0,p.gw0)(16),await new Promise(e=>requestAnimationFrame(e))}constructor(e){this.commandId=N.PASTE,this.shortcuts=["meta v","ctrl v"],this.document=e.get(p.L4w),this.selectService=e.get(p.oJU),this.entityManager=e.get(p.v2K),this.hoverService=e.get(p.XHk),this.dragService=e.get(p.iwM),this.execute=this.execute.bind(this)}}class Z{async execute(){this.selectService.selectedNodes.forEach(e=>{e.renderData.expanded=!0})}constructor(e){this.commandId=N.EXPAND,this.commandDetail={label:"Expand"},this.shortcuts=["meta alt closebracket","ctrl alt openbracket"],this.selectService=e.get(p.oJU),this.execute=this.execute.bind(this)}}class O{async execute(e){let t=Array.isArray(e)?e:this.selectService.selection;this.isValid(t.filter(e=>e instanceof p.CTO))&&(this.historyService.startTransaction(),t.forEach(e=>{e instanceof p.CTO?this.removeNode(e):e instanceof p.e2M?this.removeLine(e):e.dispose()}),this.selectService.selection=this.selectService.selection.filter(e=>!e.disposed),this.historyService.endTransaction())}isValid(e){return!e.some(e=>[ty.Start,ty.End].includes(e.flowNodeType))||(D.FN.error({content:"Start or End node cannot be deleted",showClose:!1}),!1)}removeNode(e){var t;if(!this.document.canRemove(e))return;let o=e.getNodeMeta(),n=null==(t=o.subCanvas)?void 0:t.call(o,e);if(null==n?void 0:n.isCanvas)return void n.parentNode.dispose();e.dispose()}removeLine(e){this.document.linesManager.canRemove(e)&&e.dispose()}constructor(e){this.commandId=N.DELETE,this.shortcuts=["backspace","delete"],this.document=e.get(p.L4w),this.selectService=e.get(p.oJU),this.historyService=e.get(p.qpv),this.execute=this.execute.bind(this)}}class V{async execute(){if(await this.hasSelectedText()||!this.isValid(this.selectedNodes))return;let e=this.toClipboardData();await this.write(e)}async hasSelectedText(){var e,t;return!!(null==(e=window.getSelection())?void 0:e.toString())&&(await navigator.clipboard.writeText((null==(t=window.getSelection())?void 0:t.toString())??""),D.FN.success({content:"Text copied"}),!0)}get selectedNodes(){return this.selectService.selection.filter(e=>e instanceof p.CTO)}isValid(e){return 0!==e.length||(D.FN.warning({content:"No nodes selected"}),!1)}toClipboardData(e){let t=this.getValidNodes(e||this.selectedNodes),o=this.toSource();return{type:L,source:o,json:this.toJSON(t),bounds:this.getEntireBounds(t)}}getValidNodes(e){return e.filter(e=>![ty.Start,ty.End].includes(e.flowNodeType)&&!e.getNodeMeta().copyDisable)}toSource(){return{host:window.location.host}}toJSON(e){return{nodes:this.getNodeJSONs(e),edges:this.getEdgeJSONs(e)}}getNodeJSONs(e){return e.map(e=>{var t;let o=this.document.toNodeJSON(e);if(!(null==(t=o.meta)?void 0:t.position))return o;let{bounds:n}=e.getData(p.VOn);return o.meta.position={x:n.x,y:n.y},o}).filter(Boolean)}getEdgeJSONs(e){let t=new Set,o=new Set(e.map(e=>e.id));return e.forEach(e=>{let n=e.getData(p.Z14);[...n.inputLines,...n.outputLines].forEach(e=>{var n;o.has(e.from.id)&&(null==(n=e.to)?void 0:n.id)&&o.has(e.to.id)&&t.add(e)})}),Array.from(t).map(e=>e.toJSON())}getEntireBounds(e){let t=e.map(e=>e.getData(p.VOn).bounds),o=p.AeJ.enlarge(t);return{x:o.x,y:o.y,width:o.width,height:o.height}}async write(e){try{await navigator.clipboard.writeText(JSON.stringify(e)),this.notifySuccess()}catch(e){console.error("Failed to write text: ",e)}}notifySuccess(){let e=this.selectedNodes.map(e=>e.flowNodeType);if(e.includes("start")||e.includes("end"))return void D.FN.warning({content:"The Start/End node cannot be duplicated, other nodes have been copied to the clipboard",showClose:!1});D.FN.success({content:"Nodes have been copied to the clipboard",showClose:!1})}constructor(e){this.commandId=N.COPY,this.shortcuts=["meta c","ctrl c"],this.document=e.get(p.L4w),this.selectService=e.get(p.oJU),this.execute=this.execute.bind(this)}}class P{async execute(){this.selectService.selectedNodes.forEach(e=>{e.renderData.expanded=!1})}constructor(e){this.commandId=N.COLLAPSE,this.commandDetail={label:"Collapse"},this.shortcuts=["meta alt openbracket","ctrl alt openbracket"],this.selectService=e.get(p.oJU),this.execute=this.execute.bind(this)}}function A(e,t){e.addHandlers(new V(t),new T(t),new S(t),new P(t),new Z(t),new O(t),new k(t),new M(t))}var F=o(8395);class R{save(){console.log(this.document.toJSON())}}(0,F.gn)([(0,p.f3M)(p.bQo),(0,F.w6)("design:type",void 0===p.bQo?Object:p.bQo)],R.prototype,"ctx",void 0),(0,F.gn)([(0,p.f3M)(p.z2R),(0,F.w6)("design:type",void 0===p.z2R?Object:p.z2R)],R.prototype,"selectionService",void 0),(0,F.gn)([(0,p.f3M)(p.XQj),(0,F.w6)("design:type",void 0===p.XQj?Object:p.XQj)],R.prototype,"playground",void 0),(0,F.gn)([(0,p.f3M)(p.L4w),(0,F.w6)("design:type",void 0===p.L4w?Object:p.L4w)],R.prototype,"document",void 0),R=(0,F.gn)([(0,p.b2C)()],R);class _{async addRunningNode(e){this._runningNodes.push(e),e.renderData.node.classList.add("node-running"),this.document.linesManager.forceUpdate(),await (0,p.gw0)(1e3),await Promise.all(e.blocks.map(e=>this.addRunningNode(e)));let t=e.getData(p.Z14).outputNodes;await Promise.all(t.map(e=>this.addRunningNode(e)))}async startRun(){await this.addRunningNode(this.document.getNode("start_0")),this._runningNodes.forEach(e=>{e.renderData.node.classList.remove("node-running")}),this._runningNodes=[],this.document.linesManager.forceUpdate()}isFlowingLine(e){return this._runningNodes.some(t=>t.getData(p.Z14).outputLines.includes(e))}constructor(){this._runningNodes=[]}}(0,F.gn)([(0,p.f3M)(p.XQj),(0,F.w6)("design:type",void 0===p.XQj?Object:p.XQj)],_.prototype,"playground",void 0),(0,F.gn)([(0,p.f3M)(p.L4w),(0,F.w6)("design:type",void 0===p.L4w?Object:p.L4w)],_.prototype,"document",void 0),_=(0,F.gn)([(0,p.b2C)()],_);let B=(0,p.M1y)({onInit(e,t){e.document.onNodeCreate(e=>{let{node:t}=e,o=(0,p.CMu)(t),n=t.getData(p._J4),i=e=>{if(!e)return void n.clearVar();let i=g.Kz.schemaToAST(e);if(i){var r,s;let e=(null==o?void 0:o.getValueIn("title"))||t.id;n.setVar(p.qRT.createVariableDeclaration({meta:{title:`${e}`,icon:null==(s=t.getNodeRegistry())||null==(r=s.info)?void 0:r.icon},key:`${t.id}`,type:i}))}else n.clearVar()};o&&(i(o.getValueIn("outputs")),o.onFormValuesChange(e=>{(e.name.match(/^outputs/)||e.name.match(/^title/))&&i(o.getValueIn("outputs"))}))})}});var U=o(3251);let H=U.ZP.span`
  font-size: 12px;
  color: red;
`,G=U.ZP.span`
  font-size: 12px;
  color: orange;
`,Y=e=>{let{errors:t,warnings:o,invalid:n}=e,i=e=>e?e.map(e=>(0,c.jsx)("span",{children:e.message},e.name)):null;return(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{children:(0,c.jsx)(H,{children:i(t)})}),(0,c.jsx)("div",{children:(0,c.jsx)(G,{children:i(o)})})]})},X=U.ZP.div`
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 0 12px 12px;
`,W=U.ZP.div`
  color: var(--semi-color-text-2);
  font-size: 12px;
  line-height: 20px;
  padding: 0px 4px;
  word-break: break-all;
  white-space: break-spaces;
`;function J(e){var t;let{node:o,expanded:n}=tx(),i=tm(),r=o.getNodeRegistry();return(0,c.jsx)(X,{children:n?(0,c.jsxs)(c.Fragment,{children:[i&&(0,c.jsx)(W,{children:null==(t=r.info)?void 0:t.description}),e.children]}):void 0})}let $=U.ZP.div`
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 6px;
`;function Q(e){let{name:t,type:o,isArray:n,className:i}=e,r=n?g.Cn[o]:g.Fy[o];return(0,c.jsx)(D.u,{content:(0,c.jsxs)($,{children:[r," ",o]}),children:(0,c.jsxs)(D.Vp,{color:"white",className:i,style:{padding:4,maxWidth:450},children:[r,t&&(0,c.jsxs)("span",{style:{display:"inline-block",marginLeft:4,marginTop:-1,overflow:"hidden",textOverflow:"ellipsis"},children:[" ",t]})]})})}let q=U.ZP.div`
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  border-top: 1px solid var(--semi-color-border);
  padding: 8px 0 0;
  width: 100%;

  :global(.semi-tag .semi-tag-content) {
    font-size: 10px;
  }
`;function K(){return tm()?null:(0,c.jsx)(p.gNt,{name:"outputs",children:e=>{var t;let{field:o}=e,n=null==(t=o.value)?void 0:t.properties;if(n){let e=Object.keys(n).map(e=>{let t=n[e];return(0,c.jsx)(Q,{name:e,type:t.type},e)});return(0,c.jsx)(q,{children:e})}return(0,c.jsx)(c.Fragment,{})}})}let{Text:ee}=D.Sx;function et(e){let{children:t,name:o,required:n,description:i,type:r,labelWidth:s}=e,l=(0,x.useCallback)(e=>(0,c.jsxs)("div",{style:{width:"0",display:"flex",flex:"1"},children:[(0,c.jsx)(ee,{style:{width:"100%"},ellipsis:{showTooltip:!!e},children:o}),n&&(0,c.jsx)("span",{style:{color:"#f93920",paddingLeft:"2px"},children:"*"})]}),[]);return(0,c.jsxs)("div",{style:{fontSize:12,marginBottom:6,width:"100%",position:"relative",display:"flex",justifyContent:"center",alignItems:"center",gap:8},children:[(0,c.jsxs)("div",{style:{justifyContent:"center",alignItems:"center",color:"var(--semi-color-text-0)",width:s||118,position:"relative",display:"flex",columnGap:4,flexShrink:0},children:[(0,c.jsx)(Q,{className:"form-item-type-tag",type:r}),i?(0,c.jsx)(D.u,{content:i,children:l()}):l(!0)]}),(0,c.jsx)("div",{style:{flexGrow:1,minWidth:0},children:t})]})}function eo(){let{readonly:e}=tx();return(0,c.jsx)(p.gNt,{name:"inputs",children:t=>{var o,n;let{field:i}=t,r=(null==(o=i.value)?void 0:o.required)||[],s=null==(n=i.value)?void 0:n.properties;if(!s)return(0,c.jsx)(c.Fragment,{});let l=Object.keys(s).map(t=>{let o=s[t];return(0,c.jsx)(p.gNt,{name:`inputsValues.${t}`,defaultValue:o.default,children:n=>{let{field:i,fieldState:s}=n;return(0,c.jsxs)(et,{name:t,type:o.type,required:r.includes(t),children:[(0,c.jsx)(g.XA,{value:i.value,onChange:i.onChange,readonly:e,hasError:Object.keys((null==s?void 0:s.errors)||{}).length>0,schema:o}),(0,c.jsx)(Y,{errors:null==s?void 0:s.errors})]})}},t)});return(0,c.jsx)(c.Fragment,{children:l})}})}var en=o(238),ei=o(453),er=o(4698);let es=e=>{var t;let{node:o,deleteNode:n}=e,[i,r]=(0,x.useState)(!0),s=(0,p.sX$)(),l=o.getNodeRegistry(),a=(0,p.G2Z)(b.uf),d=(0,p.G2Z)(p.oJU),u=(0,p.G2Z)(p.iwM),h=a.canMoveOutContainer(o),g=(0,x.useCallback)(()=>{r(!1),requestAnimationFrame(()=>{r(!0)})},[]),m=(0,x.useCallback)(async e=>{e.stopPropagation();let t=o.parent;a.moveOutContainer({node:o}),await a.clearInvalidLines({dragNode:o,sourceParent:t}),g(),await (0,p.gw0)(16),d.selectNode(o),u.startDragSelectedNodes(e)},[a,o,g]),f=(0,x.useCallback)(e=>{let t=new V(s),n=new T(s),i=t.toClipboardData([o]);n.apply(i),e.stopPropagation()},[s,o]),y=(0,x.useCallback)(e=>{n(),e.stopPropagation()},[s,o]);if(i)return(0,c.jsx)(D.Lt,{trigger:"hover",position:"bottomRight",render:(0,c.jsxs)(D.Lt.Menu,{children:[h&&(0,c.jsx)(D.Lt.Item,{onClick:m,children:"Move out"}),(0,c.jsx)(D.Lt.Item,{onClick:f,disabled:!0===l.meta.copyDisable,children:"Create Copy"}),(0,c.jsx)(D.Lt.Item,{onClick:y,disabled:!!((null==(t=l.canDelete)?void 0:t.call(l,s,o))||l.meta.deleteDisable),children:"Delete"})]}),children:(0,c.jsx)(D.hU,{color:"secondary",size:"small",theme:"borderless",icon:(0,c.jsx)(er.Z,{}),onClick:e=>e.stopPropagation()})})},el=U.ZP.div`
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  column-gap: 8px;
  border-radius: 8px 8px 0 0;
  cursor: move;

  background: linear-gradient(#f2f2ff 0%, rgba(0, 0, 0, 0.02) 100%);
  overflow: hidden;

  padding: 8px;
`,ea=U.ZP.div`
  font-size: 20px;
  flex: 1;
  width: 0;
`,ed=U.ZP.img`
  width: 24px;
  height: 24px;
  scale: 0.8;
  border-radius: 4px;
`,ec=U.ZP.div`
  display: flex;
  align-items: center;
  column-gap: 4px;
`,eu=e=>{var t;let o=null==(t=e.getNodeRegistry().info)?void 0:t.icon;return o?(0,c.jsx)(ed,{src:o}):null},{Text:ep}=D.Sx;function eh(){let{node:e,expanded:t,toggleExpand:o,readonly:n}=tx(),i=(0,p.sX$)(),r=tm();return(0,c.jsxs)(el,{children:[eu(e),(0,c.jsx)(ea,{children:(0,c.jsx)(p.gNt,{name:"title",children:e=>{let{field:{value:t,onChange:o},fieldState:n}=e;return(0,c.jsxs)("div",{style:{height:24},children:[(0,c.jsx)(ep,{ellipsis:{showTooltip:!0},children:t}),(0,c.jsx)(Y,{errors:null==n?void 0:n.errors})]})}})}),e.renderData.expandable&&!r&&(0,c.jsx)(D.zx,{type:"primary",icon:t?(0,c.jsx)(en.Z,{}):(0,c.jsx)(ei.Z,{}),size:"small",theme:"borderless",onClick:e=>{o(),e.stopPropagation()}}),n?void 0:(0,c.jsx)(ec,{children:(0,c.jsx)(es,{node:e,deleteNode:()=>{i.get(p.VDr).executeCommand(N.DELETE,[e])}})})]})}var eg=o(9287),ex=o(1352);let em=U.ZP.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 12px;
  margin-bottom: 6px;
`,ef=U.ZP.div`
  width: 120px;
  margin-right: 5px;
  position: relative;
`,ey=e=>{let{value:t,disabled:o}=e,[n,i]=(0,x.useState)(e.propertyKey),r=(o,n)=>{t[o]=n,e.onChange(t,e.propertyKey)},s=o=>{e.onChange({...t,...o},e.propertyKey)};return(0,x.useLayoutEffect)(()=>{i(e.propertyKey)},[e.propertyKey]),(0,c.jsxs)(em,{children:[(0,c.jsxs)(ef,{children:[(0,c.jsx)(g.Sj,{value:t,disabled:o,style:{position:"absolute",top:2,left:4,zIndex:1,padding:"0 5px",height:20},onChange:e=>s(e)}),(0,c.jsx)(D.II,{value:n,disabled:o,size:"small",onChange:e=>i(e.trim()),onBlur:()=>{""!==n?e.onChange(t,e.propertyKey,n):i(e.propertyKey)},style:{paddingLeft:26}})]}),(0,c.jsx)(g.XA,{value:t.default,onChange:e=>r("default",e),schema:t,style:{flexGrow:1}}),e.onDelete&&!o&&(0,c.jsx)(D.zx,{style:{marginLeft:5,position:"relative",top:2},size:"small",theme:"borderless",icon:(0,c.jsx)(ex.Z,{}),onClick:e.onDelete})]})},ev=e=>{let t=e.value||{},{readonly:o}=tx(),[n,i]=(0,x.useState)({key:"",value:{type:"string"}}),[r,s]=(0,x.useState)(),l=()=>{i({key:"",value:{type:"string"}}),s(!1)},a=(o,n,i)=>{let r={...t};i?(delete r[n],r[i]=o):r[n]=o,e.onChange(r)};return(0,c.jsxs)(c.Fragment,{children:[Object.keys(e.value||{}).map(n=>{let i=t[n]||{};return(0,c.jsx)(ey,{propertyKey:n,useFx:e.useFx,value:i,disabled:o,onChange:a,onDelete:()=>{let o={...t};delete o[n],e.onChange(o)}},n)}),r&&(0,c.jsx)(ey,{propertyKey:n.key,value:n.value,useFx:e.useFx,onChange:(e,o,n)=>{n?(n in t||a(e,o,n),l()):i({key:n||o,value:e})},onDelete:()=>{let{key:o}=n;setTimeout(()=>{let n={...t};delete n[o],e.onChange(n),l()},10)}}),!o&&(0,c.jsx)("div",{children:(0,c.jsx)(D.zx,{theme:"borderless",icon:(0,c.jsx)(eg.Z,{}),onClick:()=>s(!0),children:"Add"})})]})};U.ZP.div`
  background-color: var(--semi-color-fill-0);
  border-radius: var(--semi-border-radius-small);
  padding-left: 12px;
  width: 100%;
  min-height: 24px;
  line-height: 24px;
  display: flex;
  align-items: center;
  &.has-error {
    outline: red solid 1px;
  }
`;let ej={render:e=>{let{form:t}=e;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsxs)(J,{children:[(0,c.jsx)(eo,{}),(0,c.jsx)(K,{})]})]})},validateTrigger:p.C$T.onChange,validate:{title:e=>{let{value:t}=e;return t?void 0:"Title is required"},"inputsValues.*":e=>{var t;let{value:o,context:n,formValues:i,name:r}=e,s=r.replace(/^inputsValues\./,"");if(((null==(t=i.inputs)?void 0:t.required)||[]).includes(s)&&(""===o||void 0===o||(null==o?void 0:o.content)===""))return`${s} is required`}},effect:{inputsValues:g.gB}};var eC=o(2079),eb=o(3872),ew=o(4670),eL=o(9034),eN=((i={}).Title="title",i.Color="color",i);let eM=e=>{let{size:t}=e;return(0,c.jsx)("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",style:{width:t,height:t},children:(0,c.jsx)("path",{id:"group",fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M 0.009766 10 L 0.009766 9.990234 L 0 9.990234 L 0 7.5 L 1 7.5 L 1 9 L 2.5 9 L 2.5 10 L 0.009766 10 Z M 3.710938 10 L 3.710938 9 L 6.199219 9 L 6.199219 10 L 3.710938 10 Z M 7.5 10 L 7.5 9 L 9 9 L 9 7.5 L 10 7.5 L 10 9.990234 L 9.990234 9.990234 L 9.990234 10 L 7.5 10 Z M 0 6.289063 L 0 3.800781 L 1 3.800781 L 1 6.289063 L 0 6.289063 Z M 9 6.289063 L 9 3.800781 L 10 3.800781 L 10 6.289063 L 9 6.289063 Z M 0 2.5 L 0 0.009766 L 0.009766 0.009766 L 0.009766 0 L 2.5 0 L 2.5 1 L 1 1 L 1 2.5 L 0 2.5 Z M 9 2.5 L 9 1 L 7.5 1 L 7.5 0 L 9.990234 0 L 9.990234 0.009766 L 10 0.009766 L 10 2.5 L 9 2.5 Z M 3.710938 1 L 3.710938 0 L 6.199219 0 L 6.199219 1 L 3.710938 1 Z"})})},ek=e=>{let{size:t}=e;return(0,c.jsx)("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",style:{width:t,height:t},children:(0,c.jsx)("path",{id:"ungroup",fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M 9.654297 10.345703 L 8.808594 9.5 L 7.175781 9.5 L 7.175781 8.609375 L 7.917969 8.609375 L 1.390625 2.082031 L 1.390625 2.824219 L 0.5 2.824219 L 0.5 1.191406 L -0.345703 0.345703 L 0.283203 -0.283203 L 1.166016 0.599609 L 2.724609 0.599609 L 2.724609 1.490234 L 2.056641 1.490234 L 8.509766 7.943359 L 8.509766 7.275391 L 9.400391 7.275391 L 9.400391 8.833984 L 10.283203 9.716797 L 9.654297 10.345703 Z M 0.509766 9.5 L 0.509766 9.490234 L 0.5 9.490234 L 0.5 7.275391 L 1.390625 7.275391 L 1.390625 8.609375 L 2.724609 8.609375 L 2.724609 9.5 L 0.509766 9.5 Z M 3.802734 9.5 L 3.802734 8.609375 L 6.017578 8.609375 L 6.017578 9.5 L 3.802734 9.5 Z M 0.5 6.197266 L 0.5 3.982422 L 1.390625 3.982422 L 1.390625 6.197266 L 0.5 6.197266 Z M 8.509766 6.197266 L 8.509766 3.982422 L 9.400391 3.982422 L 9.400391 6.197266 L 8.509766 6.197266 Z M 8.509766 2.824219 L 8.509766 1.490234 L 7.175781 1.490234 L 7.175781 0.599609 L 9.390625 0.599609 L 9.390625 0.609375 L 9.400391 0.609375 L 9.400391 2.824219 L 8.509766 2.824219 Z M 3.802734 1.490234 L 3.802734 0.599609 L 6.017578 0.599609 L 6.017578 1.490234 L 3.802734 1.490234 Z"})})},eS=e=>{let{node:t,style:o}=e,n=(0,p.G2Z)(p.HoL);return(0,c.jsx)(D.u,{content:"Ungroup",children:(0,c.jsx)("div",{className:"workflow-group-ungroup",style:o,children:(0,c.jsx)(D.zx,{icon:(0,c.jsx)(ek,{size:14}),style:{height:30,width:30},theme:"borderless",type:"tertiary",onClick:()=>{n.executeCommand(C.I5.Ungroup,t)}})})})};var eD=o(6031);let ez=()=>{let[e,t]=(0,x.useState)(!1);return(0,c.jsx)(p.gNt,{name:eN.Title,children:o=>{let{field:n}=o;return e?(0,c.jsx)(D.II,{autoFocus:!0,className:"workflow-group-title-input",size:"small",value:n.value,onChange:n.onChange,onMouseDown:e=>e.stopPropagation(),onBlur:()=>t(!1),draggable:!1,onEnterPress:()=>t(!1)}):(0,c.jsx)("p",{className:"workflow-group-title",onDoubleClick:()=>t(!0),children:n.value??"Group"})}})},eI="Blue",eE={Red:{50:"#fef2f2",300:"#fca5a5",400:"#f87171"},Orange:{50:"#fff7ed",300:"#fdba74",400:"#fb923c"},Amber:{50:"#fffbeb",300:"#fcd34d",400:"#fbbf24"},Yellow:{50:"#fef9c3",300:"#fde047",400:"#facc15"},Lime:{50:"#f7fee7",300:"#bef264",400:"#a3e635"},Green:{50:"#f0fdf4",300:"#86efac",400:"#4ade80"},Emerald:{50:"#ecfdf5",300:"#6ee7b7",400:"#34d399"},Teal:{50:"#f0fdfa",300:"#5eead4",400:"#2dd4bf"},Cyan:{50:"#ecfeff",300:"#67e8f9",400:"#22d3ee"},Sky:{50:"#ecfeff",300:"#7dd3fc",400:"#38bdf8"},Blue:{50:"#eff6ff",300:"#93c5fd",400:"#60a5fa"},Indigo:{50:"#eef2ff",300:"#a5b4fc",400:"#818cf8"},Violet:{50:"#f5f3ff",300:"#c4b5fd",400:"#a78bfa"},Purple:{50:"#faf5ff",300:"#d8b4fe",400:"#c084fc"},Fuchsia:{50:"#fdf4ff",300:"#f0abfc",400:"#e879f9"},Pink:{50:"#fdf2f8",300:"#f9a8d4",400:"#f472b6"},Rose:{50:"#fff1f2",300:"#fda4af",400:"#fb7185"},Gray:{50:"#f9fafb",300:"#d1d5db",400:"#9ca3af"}},eT=()=>(0,c.jsx)(p.gNt,{name:eN.Color,children:e=>{let{field:t}=e,o=t.value??eI;return(0,c.jsx)(D.J2,{position:"top",mouseLeaveDelay:300,content:(0,c.jsx)("div",{className:"workflow-group-color-palette",children:Object.entries(eE).map(e=>{let[n,i]=e;return(0,c.jsx)(D.u,{content:n,mouseEnterDelay:300,children:(0,c.jsx)("span",{className:"workflow-group-color-item",style:{backgroundColor:i["300"],borderColor:n===o?i["400"]:"#fff"},onClick:()=>t.onChange(n)},n)},n)})}),children:(0,c.jsx)("span",{className:"workflow-group-color",style:{backgroundColor:eE[o]["300"]}})})}}),eZ=()=>(0,c.jsxs)("div",{className:"workflow-group-tools",children:[(0,c.jsx)(eD.Z,{className:"workflow-group-tools-drag"}),(0,c.jsx)(ez,{}),(0,c.jsx)(eT,{})]}),eO="workflow-move-into-group-tip-visible",eV="false";class eP{static get instance(){return this._instance||(this._instance=new eP),this._instance}isClosed(){return this.isCloseForever()||this.closed}close(){this.closed=!0}isCloseForever(){return localStorage.getItem(eO)===eV}closeForever(){localStorage.setItem(eO,eV)}constructor(){this.closed=!1}}let eA=()=>{let e=(0,p.PFB)(),[t,o]=(0,x.useState)(!1),n=eP.instance,i=(0,p.G2Z)(b.uf),r=(0,x.useCallback)(()=>{n.isClosed()||o(!0)},[n]),s=(0,x.useCallback)(()=>{n.close(),o(!1)},[n]),l=(0,x.useCallback)(()=>{n.closeForever(),s()},[s,n]);return(0,x.useEffect)(()=>{let t=i.on(t=>{t.type===b.DL.In&&t.targetContainer===e&&r()}),n=i.on(t=>{t.type===b.DL.Out&&(t.sourceContainer!==e||e.blocks.length||o(!1))});return()=>{t.dispose(),n.dispose()}},[i,e,r,s,t]),{visible:t,close:s,closeForever:l}},eF=U.ZP.div`
  position: absolute;
  top: 35px;

  width: 100%;
  height: 28px;
  white-space: nowrap;
  pointer-events: auto;

  .container {
    display: inline-flex;
    justify-content: center;
    height: 100%;
    width: 100%;
    background-color: rgb(255 255 255);
    border-radius: 8px 8px 0 0;

    .content {
      overflow: hidden;
      display: inline-flex;
      align-items: center;
      justify-content: flex-start;

      width: fit-content;
      height: 100%;
      padding: 0 12px;

      .text {
        font-size: 14px;
        font-weight: 400;
        font-style: normal;
        line-height: 20px;
        color: rgba(15, 21, 40, 82%);
        text-overflow: ellipsis;
        margin: 0;
      }

      .space {
        width: 128px;
      }
    }

    .actions {
      display: flex;
      gap: 8px;
      align-items: center;

      height: 28px;
      padding: 0 12px;

      .close-forever {
        cursor: pointer;

        padding: 0 3px;

        font-size: 12px;
        font-weight: 400;
        font-style: normal;
        line-height: 12px;
        color: rgba(32, 41, 69, 62%);
        margin: 0;
      }

      .close {
        display: flex;
        cursor: pointer;
        height: 100%;
        align-items: center;
      }
    }
  }
`,eR=/(Macintosh|MacIntel|MacPPC|Mac68K|iPad)/.test(navigator.userAgent),e_=()=>(0,c.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 16 16",children:(0,c.jsx)("path",{fill:"#060709",fillOpacity:"0.5",d:"M12.13 12.128a.5.5 0 0 0 .001-.706L8.71 8l3.422-3.423a.5.5 0 0 0-.001-.705.5.5 0 0 0-.706-.002L8.002 7.293 4.579 3.87a.5.5 0 0 0-.705.002.5.5 0 0 0-.002.705L7.295 8l-3.423 3.422a.5.5 0 0 0 .002.706c.**************.705.001l3.423-3.422 3.422 3.422c.**************.706-.001"})}),eB=()=>{let{visible:e,close:t,closeForever:o}=eA();return e?(0,c.jsx)(eF,{className:"workflow-group-tips",children:(0,c.jsxs)("div",{className:"container",children:[(0,c.jsxs)("div",{className:"content",children:[(0,c.jsx)("p",{className:"text",children:`Hold ${eR?"Cmd ⌘":"Ctrl"} to drag node out`}),(0,c.jsx)("div",{className:"space",style:{width:0}})]}),(0,c.jsxs)("div",{className:"actions",children:[(0,c.jsx)("p",{className:"close-forever",onClick:o,children:"Never Remind"}),(0,c.jsx)("div",{className:"close",onClick:t,children:(0,c.jsx)(e_,{})})]})]})}):null},eU=e=>{let{onMouseDown:t,onFocus:o,onBlur:n,children:i,style:r}=e,s=eE[(0,p.qoQ)(eN.Color)??eI];return(0,c.jsx)("div",{className:"workflow-group-header","data-flow-editor-selectable":"false",onMouseDown:t,onFocus:o,onBlur:n,style:{...r,backgroundColor:s["50"],borderColor:s["300"]},children:i})},eH=e=>{let{node:t,style:o}=e,n=eE[(0,p.qoQ)(eN.Color)??eI];return(0,x.useEffect)(()=>{let e=document.createElement("style");return e.textContent=`
      .workflow-group-render[data-group-id="${t.id}"] .workflow-group-background {
        border: 1px solid ${n["300"]};
      }

      .workflow-group-render.selected[data-group-id="${t.id}"] .workflow-group-background {
        border: 1px solid ${n["400"]};
      }
    `,document.head.appendChild(e),()=>{e.remove()}},[n]),(0,c.jsx)("div",{className:"workflow-group-background","data-flow-editor-selectable":"true",style:{...o,backgroundColor:`${n["300"]}29`}})},eG=()=>{let{node:e,selected:t,selectNode:o,nodeRef:n,startDrag:i,onFocus:r,onBlur:s}=(0,p.PVB)(),l=(0,b.se)(),a=e.getData(p.Psq).getFormModel(),d=null==a?void 0:a.formControl,{height:u,width:h}=l??{},g=u??0;return(0,x.useEffect)(()=>{e.renderData.node.style.pointerEvents="none"},[e]),(0,c.jsx)("div",{className:`workflow-group-render ${t?"selected":""}`,ref:n,"data-group-id":e.id,"data-node-selected":String(t),onMouseDown:o,onClick:e=>{o(e)},style:{width:h,height:u},children:(0,c.jsx)(p.l09,{control:d,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eU,{onMouseDown:e=>{i(e)},onFocus:r,onBlur:s,style:{height:30},children:(0,c.jsx)(eZ,{})}),(0,c.jsx)(eB,{}),(0,c.jsx)(eS,{node:e}),(0,c.jsx)(eH,{node:e,style:{top:35,height:g-30-5}})]})})})},eY=e=>{let{bounds:t,children:o,flowSelectConfig:n,commandRegistry:i}=e;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{style:{position:"absolute",left:t.right,top:t.top,transform:"translate(-100%, -100%)"},onMouseDown:e=>{e.stopPropagation()},children:(0,c.jsxs)(D.hE,{size:"small",style:{display:"flex",flexWrap:"nowrap",height:24},children:[(0,c.jsx)(D.u,{content:"Collapse",children:(0,c.jsx)(D.zx,{icon:(0,c.jsx)(eC.Z,{}),style:{height:24},type:"primary",theme:"solid",onMouseDown:e=>{i.executeCommand(N.COLLAPSE)}})}),(0,c.jsx)(D.u,{content:"Expand",children:(0,c.jsx)(D.zx,{icon:(0,c.jsx)(eb.Z,{}),style:{height:24},type:"primary",theme:"solid",onMouseDown:e=>{i.executeCommand(N.EXPAND)}})}),(0,c.jsx)(D.u,{content:"Create Group",children:(0,c.jsx)(D.zx,{icon:(0,c.jsx)(eM,{size:14}),style:{height:24},type:"primary",theme:"solid",onClick:()=>{i.executeCommand(C.I5.Group)}})}),(0,c.jsx)(D.u,{content:"Copy",children:(0,c.jsx)(D.zx,{icon:(0,c.jsx)(ew.Z,{}),style:{height:24},type:"primary",theme:"solid",onClick:()=>{i.executeCommand(N.COPY)}})}),(0,c.jsx)(D.u,{content:"Delete",children:(0,c.jsx)(D.zx,{type:"primary",theme:"solid",icon:(0,c.jsx)(eL.Z,{}),style:{height:24},onClick:()=>{i.executeCommand(N.DELETE)}})})]})}),(0,c.jsx)("div",{children:o})]})},eX=x.createContext({}),eW=x.createContext({visible:!1,setNodeRender:()=>{}}),eJ=x.createContext(!1);var e$=o(4578);let eQ=U.ZP.div`
  align-items: flex-start;
  background-color: #fff;
  border: 1px solid rgba(6, 7, 9, 0.15);
  border-radius: 8px;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.04), 0 4px 12px 0 rgba(0, 0, 0, 0.02);
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  min-width: 360px;
  width: 100%;
  height: auto;

  &.selected {
    border: 1px solid #4e40e5;
  }
`,eq=()=>(0,c.jsx)(e$.Z,{style:{position:"absolute",color:"red",left:-6,top:-6,zIndex:1,background:"white",borderRadius:8}}),eK=e=>{let{children:t,isScrollToView:o=!1}=e,n=tx(),{selected:i,startDrag:r,ports:s,selectNode:l,nodeRef:a,onFocus:d,onBlur:u}=n,[h,g]=(0,x.useState)(!1),m=(0,x.useContext)(eW),{form:f}=n,y=(0,p.sX$)(),v=s.map(e=>(0,c.jsx)(p.Jbc,{entity:e},e.id));return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eQ,{className:i?"selected":"",ref:a,draggable:!0,onDragStart:e=>{r(e),g(!0)},onClick:e=>{l(e),!h&&(m.setNodeRender(n),o&&function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:448,{bounds:n}=t.transform;e.playground.scrollToView({bounds:n,scrollDelta:{x:o/2,y:0},zoom:1,scrollToCenter:!0})}(y,n.node))},onMouseUp:()=>g(!1),onFocus:d,onBlur:u,"data-node-selected":String(i),style:{outline:(null==f?void 0:f.state.invalid)?"1px solid red":"none"},children:t}),v]})},e0=e=>{let{node:t}=e,o=(0,p.PVB)(),{form:n}=o,i=(0,x.useCallback)(()=>t.renderData.node||document.body,[]);return(0,c.jsx)(D.iV,{getPopupContainer:i,children:(0,c.jsx)(eX.Provider,{value:o,children:(0,c.jsxs)(eK,{children:[(null==n?void 0:n.state.invalid)&&(0,c.jsx)(eq,{}),null==n?void 0:n.render()]})})})},e1=e=>{let t=(0,p.Dcz)(),{line:o,selected:n=!1,hovered:i}=e;return!o.disposed&&!t.config.readonly&&(!!n||!!i)},e2=()=>(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsxs)("g",{id:"add",children:[(0,c.jsx)("path",{id:"background",fill:"#ffffff",fillRule:"evenodd",stroke:"none",d:"M 24 12 C 24 5.372583 18.627417 0 12 0 C 5.372583 0 -0 5.372583 -0 12 C -0 18.627417 5.372583 24 12 24 C 18.627417 24 24 18.627417 24 12 Z"}),(0,c.jsx)("path",{id:"content",fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M 22 12.005 C 22 6.482153 17.522848 2.004999 12 2.004999 C 6.477152 2.004999 2 6.482153 2 12.005 C 2 17.527847 6.477152 22.004999 12 22.004999 C 17.522848 22.004999 22 17.527847 22 12.005 Z"}),(0,c.jsx)("path",{id:"cross",fill:"#ffffff",stroke:"none",d:"M 11.411996 16.411797 C 11.411996 16.736704 11.675362 17 12.00023 17 C 12.325109 17 12.588474 16.736704 12.588474 16.411797 L 12.588474 12.58826 L 16.41201 12.58826 C 16.736919 12.58826 17.000216 12.324894 17.000216 12.000015 C 17.000216 11.675147 16.736919 11.411781 16.41201 11.411781 L 12.588474 11.411781 L 12.588474 7.588234 C 12.588474 7.263367 12.325109 7 12.00023 7 C 11.675362 7 11.411996 7.263367 11.411996 7.588234 L 11.411996 11.411781 L 7.588449 11.411781 C 7.263581 11.411781 7.000215 11.675147 7.000215 12.000015 C 7.000215 12.324894 7.263581 12.58826 7.588449 12.58826 L 11.411996 12.58826 L 11.411996 16.411797 Z"})]})}),e3=e=>{let{line:t,selected:o,hovered:n,color:i}=e,r=e1({line:t,selected:o,hovered:n}),s=(0,p.G2Z)(v.sS),l=(0,p.G2Z)(p.L4w),a=(0,p.G2Z)(p.iwM),d=(0,p.G2Z)(p.dmf),u=(0,p.G2Z)(p.qpv),{fromPort:h,toPort:g}=t,m=(0,x.useCallback)(async()=>{let e={x:(t.position.from.x+t.position.to.x)/2,y:(t.position.from.y+t.position.to.y)/2},o=v.QV.getContainerNode({fromPort:h}),n=await s.singleSelectNodePanel({position:e,containerNode:o,panelProps:{enableScrollClose:!0}});if(!n)return;let{nodeType:i,nodeJSON:r}=n,c=v.QV.adjustNodePosition({nodeType:i,position:e,fromPort:h,toPort:g,containerNode:o,document:l,dragService:a}),x=l.createWorkflowNodeByType(i,c,r??{},null==o?void 0:o.id);h&&g&&v.QV.subNodesAutoOffset({node:x,fromPort:h,toPort:g,containerNode:o,historyService:u,dragService:a,linesManager:d}),await (0,p.gw0)(20),v.QV.buildLine({fromPort:h,node:x,toPort:g,linesManager:d}),t.dispose()},[]);return r?(0,c.jsx)("div",{className:"line-add-button",style:{left:"50%",top:"50%",color:i},"data-testid":"sdk.workflow.canvas.line.add","data-line-id":t.id,onClick:m,children:(0,c.jsx)(e2,{})}):(0,c.jsx)(c.Fragment,{})},e5=()=>(0,c.jsx)("div",{className:"node-placeholder","data-testid":"workflow.detail.node-panel.placeholder",children:(0,c.jsx)(D.Od,{className:"node-placeholder-skeleton",loading:!0,active:!0,placeholder:(0,c.jsxs)("div",{className:"",children:[(0,c.jsxs)("div",{className:"node-placeholder-hd",children:[(0,c.jsx)(D.Od.Avatar,{shape:"square",className:"node-placeholder-avatar"}),(0,c.jsx)(D.Od.Title,{style:{width:141}})]}),(0,c.jsxs)("div",{className:"node-placeholder-content",children:[(0,c.jsxs)("div",{className:"node-placeholder-footer",children:[(0,c.jsx)(D.Od.Title,{style:{width:85}}),(0,c.jsx)(D.Od.Title,{style:{width:241}})]}),(0,c.jsx)(D.Od.Title,{style:{width:220}})]})]})})}),e9=U.ZP.div`
  width: 100%;
  height: 32px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 19px;
  padding: 0 15px;
  &:hover {
    background-color: hsl(252deg 62% 55% / 9%);
    color: hsl(252 62% 54.9%);
  }
`,e4=U.ZP.div`
  font-size: 12px;
  margin-left: 10px;
`;function e6(e){return(0,c.jsxs)(e9,{"data-testid":`demo-free-node-list-${e.label}`,onClick:e.disabled?void 0:e.onClick,style:e.disabled?{opacity:.3}:{},children:[(0,c.jsx)("div",{style:{fontSize:14},children:e.icon}),(0,c.jsx)(e4,{children:e.label})]})}let e8=U.ZP.div`
  max-height: 500px;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
`,e7=e=>{let{onSelect:t}=e,o=(0,p.sX$)(),n=(e,n)=>{var i;let r=null==(i=n.onAdd)?void 0:i.call(n,o);t({nodeType:n.type,selectEvent:e,nodeJSON:r})};return(0,c.jsx)(e8,{style:{width:180},children:tZ.map(e=>{var t,i;return(0,c.jsx)(e6,{disabled:!((null==(t=e.canAdd)?void 0:t.call(e,o))??!0),icon:(0,c.jsx)("img",{style:{width:10,height:10,borderRadius:4},src:null==(i=e.info)?void 0:i.icon}),label:e.type,onClick:t=>n(t,e)},e.type)})})},te=e=>{let{onSelect:t,position:o,onClose:n,panelProps:i}=e,{enableNodePlaceholder:r}=i;return(0,c.jsx)(D.J2,{trigger:"click",visible:!0,onVisibleChange:e=>e?null:n(),content:(0,c.jsx)(e7,{onSelect:t}),placement:"right",popupAlign:{offset:[30,0]},overlayStyle:{padding:0},children:(0,c.jsx)("div",{style:r?{position:"absolute",top:o.y-61.5,left:o.x,width:360,height:100}:{position:"absolute",top:o.y,left:o.x,width:0,height:0},children:r&&(0,c.jsx)(e5,{})})})};var tt=((r={}).Size="size",r.Note="note",r),to=((s={}).Change="change",s.MultiSelect="multiSelect",s.Select="select",s.Blur="blur",s);let tn=e=>{let{model:t,height:o}=e,n=(0,p.Dcz)(),[i,r]=(0,x.useState)(!1),s=(0,x.useCallback)(()=>!!t.element&&t.element.scrollHeight>t.element.clientHeight,[t,o,n]),l=(0,x.useCallback)(()=>{r(s())},[s]);return(0,x.useEffect)(()=>{l()},[o,l]),(0,x.useEffect)(()=>{let e=t.on(e=>{e.type===to.Change&&l()});return()=>{e.dispose()}},[t,l]),{overflow:i,updateOverflow:l}};class ti{get value(){return this.innerValue}setValue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.initialized&&e!==this.innerValue&&(this.innerValue=e,this.syncEditorValue(),this.emitter.fire({type:to.Change,value:this.innerValue}))}set element(e){this.initialized||(this.editor=e)}get element(){return this.editor}setFocus(e){this.initialized&&(e&&!this.focused?this.editor.focus():!e&&this.focused&&(this.editor.blur(),this.deselect(),this.emitter.fire({type:to.Blur})))}selectEnd(){if(!this.initialized)return;let{length:e}=this.editor.value;this.editor.setSelectionRange(e,e)}get focused(){return document.activeElement===this.editor}deselect(){let e=window.getSelection();e&&e.removeAllRanges()}get initialized(){return!!this.editor}syncEditorValue(){this.initialized&&(this.editor.value=this.innerValue)}constructor(){this.innerValue="",this.emitter=new p.Q5$,this.on=this.emitter.event}}let tr=()=>{let e=(0,p.kmj)(),{selected:t}=(0,p.PVB)(),o=e.getData(p.Psq).getFormModel(),n=(0,x.useMemo)(()=>new ti,[]);return(0,x.useEffect)(()=>{t||n.setFocus(t)},[t,n]),(0,x.useEffect)(()=>{let e=o.getValueIn(tt.Note);n.setValue(e),n.selectEnd()},[o,n]),(0,x.useEffect)(()=>{let e=o.onFormValuesChange(e=>{let{name:t}=e;if(t!==tt.Note)return;let i=o.getValueIn(tt.Note);n.setValue(i)});return()=>e.dispose()},[o,n]),n},ts=()=>{let e=(0,p.PFB)(),t=e.getNodeMeta(),o=(0,p.Dcz)(),n=(0,p.G2Z)(p.qpv),{size:i={width:240,height:150}}=t,r=e.getData(p.VOn),s=e.getData(p.Psq).getFormModel(),l=s.getValueIn(tt.Size),[a,d]=(0,x.useState)((null==l?void 0:l.width)??i.width),[c,u]=(0,x.useState)((null==l?void 0:l.height)??i.height);(0,x.useEffect)(()=>{s.getValueIn(tt.Size)||s.setValueIn(tt.Size,{width:a,height:c})},[s,a,c]),(0,x.useEffect)(()=>{let e=s.onFormValuesChange(e=>{let{name:t}=e;if(t!==tt.Size)return;let o=s.getValueIn(tt.Size);o&&(d(o.width),u(o.height))});return()=>e.dispose()},[s]);let h=(0,x.useCallback)(()=>{let t={width:a,height:c,originalWidth:a,originalHeight:c,positionX:r.position.x,positionY:r.position.y,offsetX:0,offsetY:0};return{resizing:e=>{if(!t)return;let{zoom:n}=o.config,i=e.top/n,s=e.right/n,l=e.bottom/n,a=e.left/n,c=Math.max(120,t.originalWidth+s-a),p=Math.max(80,t.originalHeight+l-i),h=(a>0||s<0)&&c<=120?t.offsetX:a/2+s/2,g=(i>0||l<0)&&p<=80?t.offsetY:i,x=t.positionX+h,m=t.positionY+g;t.width=c,t.height=p,t.offsetX=h,t.offsetY=g,d(c),u(p),r.update({position:{x:x,y:m}})},resizeEnd:()=>{n.transact(()=>{n.pushOperation({type:p.Nj8.dragNodes,value:{ids:[e.id],value:[{x:t.positionX+t.offsetX,y:t.positionY+t.offsetY}],oldValue:[{x:t.positionX,y:t.positionY}]}},{noApply:!0}),s.setValueIn(tt.Size,{width:t.width,height:t.height})})}}},[e,a,c,r,o,s,n]);return{width:a,height:c,onResize:h}},tl=e=>{let{node:t,focused:o,deleteNode:n}=e;return(0,c.jsx)("div",{className:`workflow-comment-more-button ${o?"workflow-comment-more-button-focused":""}`,children:(0,c.jsx)(es,{node:t,deleteNode:n})})},ta=e=>{let{model:t,style:o,onChange:n}=e,i=(0,p.Dcz)(),r=(0,x.useRef)(null),s=t.value||t.focused?void 0:"Enter a comment...";return(0,x.useEffect)(()=>{let e=t.on(e=>{e.type===to.Change&&(null==n||n(t.value))});return()=>e.dispose()},[t,n]),(0,x.useEffect)(()=>{r.current&&(t.element=r.current)},[r]),(0,c.jsxs)("div",{className:"workflow-comment-editor",children:[(0,c.jsx)("p",{className:"workflow-comment-editor-placeholder",children:s}),(0,c.jsx)("textarea",{className:"workflow-comment-editor-textarea",ref:r,style:o,readOnly:i.config.readonly,onChange:e=>{let{value:o}=e.target;t.setValue(o)},onFocus:()=>{t.setFocus(!0)},onBlur:()=>{t.setFocus(!1)}})]})},td=e=>{let{model:t,stopEvent:o=!0,style:n}=e,i=(0,p.Dcz)(),{startDrag:r,onFocus:s,onBlur:l,selectNode:a}=(0,p.PVB)();return(0,c.jsx)("div",{className:"workflow-comment-drag-area","data-flow-editor-selectable":"false",draggable:!0,style:n,onMouseDown:e=>{o&&(e.preventDefault(),e.stopPropagation()),t.setFocus(!1),r(e),a(e),i.node.focus()},onFocus:s,onBlur:l})},tc=e=>{let{model:t,focused:o,overflow:n}=e,i=(0,p.Dcz)(),{selectNode:r}=(0,p.PVB)(),[s,l]=(0,x.useState)(!1);return(0,x.useEffect)(()=>{o||l(!1)},[o]),(0,c.jsx)("div",{className:"workflow-comment-content-drag-area",onMouseDown:e=>{if(s)return;e.preventDefault(),e.stopPropagation(),t.setFocus(!1),r(e),i.node.focus();let o=e.clientX,n=e.clientY,a=e=>{let t=e.clientX-o,i=e.clientY-n;5>Math.abs(t)&&5>Math.abs(i)&&l(!0),document.removeEventListener("mouseup",a),document.removeEventListener("click",a)};document.addEventListener("mouseup",a),document.addEventListener("click",a)},onWheel:e=>{let o=t.element;if(s||!n||!o)return;e.stopPropagation();let i=o.scrollHeight-o.clientHeight,r=Math.min(Math.max(o.scrollTop+e.deltaY,0),i);o.scroll(0,r)},style:{display:s?"none":void 0},children:(0,c.jsx)(td,{style:{position:"relative",width:"100%",height:"100%"},model:t,stopEvent:!1})})},tu=e=>{let{focused:t,children:o,style:n}=e;return(0,c.jsx)("div",{className:"workflow-comment-container","data-flow-editor-selectable":"false",style:{outline:t?"1px solid #FF811A":"1px solid #F2B600",backgroundColor:t?"#FFF3EA":"#FFFBED",scrollbarWidth:"thin",scrollbarColor:"rgb(159 159 158 / 65%) transparent","&:WebkitScrollbar":{width:"4px"},"&::WebkitScrollbarTrack":{background:"transparent"},"&::WebkitScrollbarThumb":{backgroundColor:"rgb(159 159 158 / 65%)",borderRadius:"20px",border:"2px solid transparent"},...n},children:o})},tp=e=>{let{model:t,onResize:o,getDelta:n,style:i}=e,r=(0,p.Dcz)(),{selectNode:s}=(0,p.PVB)();return(0,c.jsx)("div",{className:"workflow-comment-resize-area",style:i,"data-flow-editor-selectable":"false",onMouseDown:e=>{if(e.preventDefault(),e.stopPropagation(),!o)return;let{resizing:i,resizeEnd:l}=o();t.setFocus(!1),s(e),r.node.focus();let a=e.clientX,d=e.clientY,c=e=>{let t=e.clientX-a,o=e.clientY-d,r=null==n?void 0:n({x:t,y:o});r&&i&&i(r)},u=()=>{l(),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",u),document.removeEventListener("click",u)};document.addEventListener("mousemove",c),document.addEventListener("mouseup",u),document.addEventListener("click",u)}})},th=e=>{let{model:t,overflow:o,onResize:n}=e;return(0,c.jsxs)("div",{style:{zIndex:999},children:[(0,c.jsx)(td,{style:{position:"absolute",left:-10,top:10,width:20,height:"calc(100% - 20px)"},model:t}),(0,c.jsx)(td,{style:{position:"absolute",right:-10,top:10,height:"calc(100% - 20px)",width:o?10:20},model:t}),(0,c.jsx)(td,{style:{position:"absolute",top:-10,left:10,width:"calc(100% - 20px)",height:20},model:t}),(0,c.jsx)(td,{style:{position:"absolute",bottom:-10,left:10,width:"calc(100% - 20px)",height:20},model:t}),(0,c.jsx)(tp,{style:{position:"absolute",left:0,top:0,cursor:"nwse-resize"},model:t,getDelta:e=>{let{x:t,y:o}=e;return{top:o,right:0,bottom:0,left:t}},onResize:n}),(0,c.jsx)(tp,{style:{position:"absolute",right:0,top:0,cursor:"nesw-resize"},model:t,getDelta:e=>{let{x:t,y:o}=e;return{top:o,right:t,bottom:0,left:0}},onResize:n}),(0,c.jsx)(tp,{style:{position:"absolute",right:0,bottom:0,cursor:"nwse-resize"},model:t,getDelta:e=>{let{x:t,y:o}=e;return{top:0,right:t,bottom:o,left:0}},onResize:n}),(0,c.jsx)(tp,{style:{position:"absolute",left:0,bottom:0,cursor:"nesw-resize"},model:t,getDelta:e=>{let{x:t,y:o}=e;return{top:0,right:0,bottom:o,left:t}},onResize:n})]})},tg=e=>{let{node:t}=e,o=tr(),{selected:n,selectNode:i,nodeRef:r,deleteNode:s}=(0,p.PVB)(),l=t.getData(p.Psq).getFormModel(),a=null==l?void 0:l.formControl,{width:d,height:u,onResize:h}=ts(),{overflow:g,updateOverflow:x}=tn({model:o,height:u});return(0,c.jsx)("div",{className:"workflow-comment",style:{width:d,height:u},ref:r,"data-node-selected":String(n),onMouseEnter:x,onMouseDown:e=>{setTimeout(()=>{i(e)},20)},children:(0,c.jsx)(p.l09,{control:a,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(tu,{focused:n,style:{height:u},children:(0,c.jsx)(p.gNt,{name:tt.Note,children:e=>{let{field:i}=e;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(ta,{model:o,value:i.value,onChange:i.onChange}),(0,c.jsx)(tc,{model:o,focused:n,overflow:g}),(0,c.jsx)(tl,{node:t,focused:n,deleteNode:s})]})}})}),(0,c.jsx)(th,{model:o,overflow:g,onResize:h})]})})})};function tx(){return(0,x.useContext)(eX)}function tm(){return(0,x.useContext)(eJ)}let tf={render:e=>{let{form:t}=e;return tm()?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsx)(J,{children:(0,c.jsx)(p.gNt,{name:"outputs",render:e=>{let{field:{value:t,onChange:o}}=e;return(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(g._R,{value:t,onChange:e=>o(e)})})}})})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsx)(J,{children:(0,c.jsx)(K,{})})]})},validateTrigger:p.C$T.onChange,validate:{title:e=>{let{value:t}=e;return t?void 0:"Title is required"}}};var ty=((l={}).Start="start",l.End="end",l.LLM="llm",l.Condition="condition",l.Loop="loop",l.Comment="comment",l);let tv={type:ty.Start,meta:{isStart:!0,deleteDisable:!0,copyDisable:!0,defaultPorts:[{type:"output"}],size:{width:360,height:211}},info:{icon:h,description:"The starting node of the workflow, used to set the information needed to initiate the workflow."},formMeta:tf,canAdd:()=>!1},tj=o.p+"static/image/icon-loop.89b07eed.jpg",tC=0,tb={type:ty.Loop,info:{icon:tj,description:"Used to repeatedly execute a series of tasks by setting the number of iterations and logic."},meta:{isContainer:!0,size:{width:560,height:400},padding:()=>({top:125,bottom:100,left:100,right:100}),selectable:(e,t)=>!t||!e.getData(p.eGM).bounds.contains(t.x,t.y),expandable:!1},onAdd:()=>({id:`loop_${(0,z.x0)(5)}`,type:"loop",data:{title:`Loop_${++tC}`}}),formMeta:{...ej,render:e=>{let{form:t}=e,o=tm(),{readonly:n}=tx(),i=(0,c.jsx)(p.gNt,{name:"batchFor",children:e=>{var t;let{field:o,fieldState:i}=e;return(0,c.jsxs)(et,{name:"batchFor",type:"array",required:!0,children:[(0,c.jsx)(g.uA,{style:{width:"100%"},value:null==(t=o.value)?void 0:t.content,onChange:e=>o.onChange({type:"ref",content:e}),readonly:n,hasError:Object.keys((null==i?void 0:i.errors)||{}).length>0}),(0,c.jsx)(Y,{errors:null==i?void 0:i.errors})]})}});return o?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsxs)(J,{children:[i,(0,c.jsx)(K,{})]})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsxs)(J,{children:[i,(0,c.jsx)(b.NT,{}),(0,c.jsx)(K,{})]})]})},effect:{batchFor:g.lz}}},tw=o.p+"static/image/icon-llm.de446995.jpg",tL=0,tN={type:ty.LLM,info:{icon:tw,description:"Call the large language model and use variables and prompt words to generate responses."},meta:{size:{width:360,height:305}},onAdd:()=>({id:`llm_${(0,z.x0)(5)}`,type:"llm",data:{title:`LLM_${++tL}`,inputsValues:{modelType:{type:"constant",content:"gpt-3.5-turbo"},temperature:{type:"constant",content:.5},systemPrompt:{type:"constant",content:"You are an AI assistant."},prompt:{type:"constant",content:""}},inputs:{type:"object",required:["modelType","temperature","prompt"],properties:{modelType:{type:"string"},temperature:{type:"number"},systemPrompt:{type:"string"},prompt:{type:"string"}}},outputs:{type:"object",properties:{result:{type:"string"}}}}})},tM=o.p+"static/image/icon-end.ac3dc81e.jpg";var tk=o(6352);let tS={...ej,render:()=>tm()?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsxs)(J,{children:[(0,c.jsx)(p.gNt,{name:"outputs.properties",render:e=>{let{field:{value:t,onChange:o}}=e;return(0,c.jsx)(p.gNt,{name:"inputsValues",children:e=>{let{field:{value:n,onChange:i}}=e,r=(0,tk.Z)(t,(e,t)=>({...e,default:null==n?void 0:n[t]}));return(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(ev,{value:r,onChange:e=>{let t=(0,tk.Z)(e,e=>e.default),n=(0,tk.Z)(e,e=>(delete e.default,e));i(t),o(n)},useFx:!0})})}})}}),(0,c.jsx)(K,{})]})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsx)(J,{children:(0,c.jsx)(K,{})})]})},tD={type:ty.End,meta:{deleteDisable:!0,copyDisable:!0,defaultPorts:[{type:"input"}],size:{width:360,height:211}},info:{icon:tM,description:"The final node of the workflow, used to return the result information after the workflow is run."},formMeta:tS,canAdd:()=>!1},tz=U.ZP.div`
  position: absolute;
  right: -12px;
  top: 50%;
`;function tI(){let{readonly:e}=tx();return(0,c.jsx)(p.F26,{name:"conditions",children:t=>{let{field:o}=t;return(0,c.jsxs)(c.Fragment,{children:[o.map((t,n)=>(0,c.jsx)(p.gNt,{name:t.name,children:t=>{let{field:i,fieldState:r}=t;return(0,c.jsxs)(et,{name:"if",type:"boolean",required:!0,labelWidth:40,children:[(0,c.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,c.jsx)(g.iB,{readonly:e,style:{flexGrow:1},value:i.value.value,onChange:e=>i.onChange({value:e,key:i.value.key})}),(0,c.jsx)(D.zx,{theme:"borderless",icon:(0,c.jsx)(ex.Z,{}),onClick:()=>o.delete(n)})]}),(0,c.jsx)(Y,{errors:null==r?void 0:r.errors,invalid:null==r?void 0:r.invalid}),(0,c.jsx)(tz,{"data-port-id":i.value.key,"data-port-type":"output"})]})}},t.name)),!e&&(0,c.jsx)("div",{children:(0,c.jsx)(D.zx,{theme:"borderless",icon:(0,c.jsx)(eg.Z,{}),onClick:()=>o.append({key:`if_${(0,z.x0)(6)}`,value:{type:"expression",content:""}}),children:"Add"})})]})}})}let tE={render:e=>{let{form:t}=e;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eh,{}),(0,c.jsx)(J,{children:(0,c.jsx)(tI,{})})]})},validateTrigger:p.C$T.onChange,validate:{title:e=>{let{value:t}=e;return t?void 0:"Title is required"},"conditions.*":e=>{let{value:t}=e;if(!(null==t?void 0:t.value))return"Condition is required"}}},tT=[{type:ty.Condition,info:{icon:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iNDQiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA0NCA0NSIgZmlsbD0ibm9uZSIgY2xhc3M9ImluamVjdGVkLXN2ZyIgZGF0YS1zcmM9Imh0dHBzOi8vbGYzLXN0YXRpYy5ieXRlZG5zZG9jLmNvbS9vYmovZWRlbi1jbi91dnBhaHR2YWJoX2xtX3poaHdoL2xqaHdadGhsYXVramxrdWx6bHAvYWN0aXZpdHlfaWNvbnMvZXhjbHVzaXZlLXNwbGl0LTA1MTguc3ZnIj4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE2LjQ3MDUgMTQuMDE1MkMxNS4yOTkgMTIuODQzNiAxNS4yOTkgMTAuOTQ0IDE2LjQ3MDUgOS43NzI0NEwyMC43MTMxIDUuNTI5N0MyMS44ODQ2IDQuMzU4MSAyMy43ODQgNC4zNTgxIDI0Ljk1NTYgNS41Mjk3TDI5LjE5ODEgOS43NzI0NEMzMC4zNjk3IDEwLjk0NCAzMC4zNjk3IDEyLjg0MzYgMjkuMTk4MSAxNC4wMTUyTDI1LjEyMDYgMTguMDkyOUgzMi42Njc0QzM2LjUzMzQgMTguMDkyOSAzOS42Njc0IDIxLjIyNjkgMzkuNjY3NCAyNS4wOTI5VjMzLjE1NFYzMy4zMjcxVjM3LjE1NEMzOS42Njc0IDM4LjI1ODUgMzguNzcxOSAzOS4xNTQgMzcuNjY3NCAzOS4xNTRIMzMuNjY3NEMzMi41NjI4IDM5LjE1NCAzMS42Njc0IDM4LjI1ODUgMzEuNjY3NCAzNy4xNTRWMzMuMzI3MVYzMy4xNTRWMjYuMDkyOUgyMy41OTQ4SDE1LjY2NzRWMzMuMTMyN0wxNy4yNjg1IDMzLjEyNDRDMTguODM5NyAzMy4xMTYzIDE5LjYzMjIgMzUuMDE1NiAxOC41MjEyIDM2LjEyNjZMMTIuNzM3NCA0MS45MTAzQzEyLjA1MDYgNDIuNTk3MSAxMC45MzcxIDQyLjU5NzEgMTAuMjUwMyA0MS45MTAzTDQuNTI1ODggMzYuMTg1OUMzLjQyMTA3IDM1LjA4MTEgNC4xOTc5NyAzMy4xOTE3IDUuNzYwMzggMzMuMTgzN0w3LjY2NzM3IDMzLjE3MzlWMjUuMDkyOUM3LjY2NzM3IDIxLjIyNyAxMC44MDE0IDE4LjA5MjkgMTQuNjY3NCAxOC4wOTI5SDIwLjU0ODFMMTYuNDcwNSAxNC4wMTUyWiIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzI3NTJfMTgzNzAyLTcpIi8+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMjc1Ml8xODM3MDItNyIgeDE9IjM4LjUyIiB5MT0iNDMuMzkxNSIgeDI9IjguMDk2ODYiIHkyPSI0LjY5ODIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KICAgICAgPHN0b3Agc3RvcC1jb2xvcj0iIzMzNzBGRiIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjAuOTk3OTA4IiBzdG9wLWNvbG9yPSIjMzNBOUZGIi8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KPC9zdmc+Cg==",description:"Connect multiple downstream branches. Only the corresponding branch will be executed if the set conditions are met."},meta:{defaultPorts:[{type:"input"}],useDynamicPort:!0,expandable:!1},formMeta:tE,onAdd:()=>({id:`condition_${(0,z.x0)(5)}`,type:"condition",data:{title:"Condition",conditions:[{key:`if_${(0,z.x0)(5)}`,value:{}},{key:`if_${(0,z.x0)(5)}`,value:{}}]}})},tv,tD,tN,tb,{type:ty.Comment,meta:{disableSideBar:!0,defaultPorts:[],renderKey:ty.Comment,size:{width:240,height:150}},formMeta:{render:()=>(0,c.jsx)(c.Fragment,{})},getInputPoints:()=>[],getOutputPoints:()=>[]}],tZ=tT.filter(e=>e.type!==ty.Comment),tO={nodes:[{id:"start_0",type:"start",meta:{position:{x:180,y:381.75}},data:{title:"Start",outputs:{type:"object",properties:{query:{type:"string",default:"Hello Flow."},enable:{type:"boolean",default:!0},array_obj:{type:"array",items:{type:"object",properties:{int:{type:"number"},str:{type:"string"}}}}}}}},{id:"condition_0",type:"condition",meta:{position:{x:640,y:363.25}},data:{title:"Condition",conditions:[{key:"if_0",value:{left:{type:"ref",content:["start_0","query"]},operator:"contains",right:{type:"constant",content:"Hello Flow."}}},{key:"if_f0rOAt",value:{left:{type:"ref",content:["start_0","enable"]},operator:"is_true"}}]}},{id:"end_0",type:"end",meta:{position:{x:2220,y:381.75}},data:{title:"End",outputs:{type:"object",properties:{result:{type:"string"}}}}},{id:"loop_H8M3U",type:"loop",meta:{position:{x:1020,y:547.96875}},data:{title:"Loop_2",batchFor:{type:"ref",content:["start_0","array_obj"]},outputs:{type:"object",properties:{result:{type:"string"}}}},blocks:[{id:"llm_CBdCg",type:"llm",meta:{position:{x:180,y:0}},data:{title:"LLM_4",inputsValues:{modelType:{type:"constant",content:"gpt-3.5-turbo"},temperature:{type:"constant",content:.5},systemPrompt:{type:"constant",content:"You are an AI assistant."},prompt:{type:"constant",content:""}},inputs:{type:"object",required:["modelType","temperature","prompt"],properties:{modelType:{type:"string"},temperature:{type:"number"},systemPrompt:{type:"string"},prompt:{type:"string"}}},outputs:{type:"object",properties:{result:{type:"string"}}}}},{id:"llm_gZafu",type:"llm",meta:{position:{x:640,y:0}},data:{title:"LLM_5",inputsValues:{modelType:{type:"constant",content:"gpt-3.5-turbo"},temperature:{type:"constant",content:.5},systemPrompt:{type:"constant",content:"You are an AI assistant."},prompt:{type:"constant",content:""}},inputs:{type:"object",required:["modelType","temperature","prompt"],properties:{modelType:{type:"string"},temperature:{type:"number"},systemPrompt:{type:"string"},prompt:{type:"string"}}},outputs:{type:"object",properties:{result:{type:"string"}}}}}],edges:[{sourceNodeID:"llm_CBdCg",targetNodeID:"llm_gZafu"}]},{id:"159623",type:"comment",meta:{position:{x:640,y:522.46875}},data:{size:{width:240,height:150},note:"hi ~\n\nthis is a comment node\n\n- flowgram.ai"}},{id:"group_V-_st",type:"group",meta:{position:{x:1020,y:96.25}},data:{title:"LLM_Group",color:"Violet"},blocks:[{id:"llm_0",type:"llm",meta:{position:{x:640,y:0}},data:{title:"LLM_0",inputsValues:{modelType:{type:"constant",content:"gpt-3.5-turbo"},temperature:{type:"constant",content:.5},systemPrompt:{type:"constant",content:"You are an AI assistant."},prompt:{type:"constant",content:""}},inputs:{type:"object",required:["modelType","temperature","prompt"],properties:{modelType:{type:"string"},temperature:{type:"number"},systemPrompt:{type:"string"},prompt:{type:"string"}}},outputs:{type:"object",properties:{result:{type:"string"}}}}},{id:"llm_l_TcE",type:"llm",meta:{position:{x:180,y:0}},data:{title:"LLM_1",inputsValues:{modelType:{type:"constant",content:"gpt-3.5-turbo"},temperature:{type:"constant",content:.5},systemPrompt:{type:"constant",content:"You are an AI assistant."},prompt:{type:"constant",content:""}},inputs:{type:"object",required:["modelType","temperature","prompt"],properties:{modelType:{type:"string"},temperature:{type:"number"},systemPrompt:{type:"string"},prompt:{type:"string"}}},outputs:{type:"object",properties:{result:{type:"string"}}}}}],edges:[{sourceNodeID:"llm_l_TcE",targetNodeID:"llm_0"},{sourceNodeID:"llm_0",targetNodeID:"end_0"},{sourceNodeID:"condition_0",targetNodeID:"llm_l_TcE",sourcePortID:"if_0"}]}],edges:[{sourceNodeID:"start_0",targetNodeID:"condition_0"},{sourceNodeID:"condition_0",targetNodeID:"llm_l_TcE",sourcePortID:"if_0"},{sourceNodeID:"condition_0",targetNodeID:"loop_H8M3U",sourcePortID:"if_f0rOAt"},{sourceNodeID:"llm_0",targetNodeID:"end_0"},{sourceNodeID:"loop_H8M3U",targetNodeID:"end_0"}]};var tV=o(6892),tP=o(5017);let tA=()=>{let e=(0,p.Dcz)();return(0,x.useCallback)(t=>e.config.getPosFromMouseEvent({clientX:t.left+64,clientY:t.top-7}),[e])},tF=()=>{let e=(0,p.G2Z)(p.oJU);return(0,x.useCallback)(t=>{t&&e.selectNode(t)},[e])},tR=()=>{let e=(0,p.G2Z)(p.L4w),t=(0,p.G2Z)(v.sS),o=(0,p.Dcz)(),n=tA(),i=tF();return(0,x.useCallback)(async o=>{let r=n(o);await new Promise(o=>{t.callNodePanel({position:r,enableMultiAdd:!0,panelProps:{},onSelect:async t=>{if(!t)return;let{nodeType:o,nodeJSON:n}=t;i(e.createWorkflowNodeByType(o,void 0,n??{}))},onClose:()=>{o()}})})},[n,t,o.config.zoom,e,i])},t_=e=>{let t=tR();return(0,c.jsx)(D.zx,{"data-testid":"demo.free-layout.add-node",icon:(0,c.jsx)(eg.Z,{}),color:"highlight",style:{backgroundColor:"rgba(171,181,255,0.3)",borderRadius:"8px"},disabled:e.disabled,onClick:e=>{t(e.currentTarget.getBoundingClientRect())},children:"Add Node"})},tB=U.ZP.div`
  position: absolute;
  bottom: 16px;
  display: flex;
  justify-content: left;
  min-width: 360px;
  pointer-events: none;
  gap: 8px;

  z-index: 99;
`,tU=U.ZP.div`
  display: flex;
  align-items: center;
  background-color: #fff;
  border: 1px solid rgba(68, 83, 130, 0.25);
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 2px 6px 0px, rgba(0, 0, 0, 0.02) 0px 4px 12px 0px;
  column-gap: 2px;
  height: 40px;
  padding: 0 4px;
  pointer-events: auto;
`,tH=U.ZP.span`
  padding: 4px;
  border-radius: 8px;
  border: 1px solid rgba(68, 83, 130, 0.25);
  font-size: 12px;
  width: 50px;
  cursor: pointer;
`,tG=U.ZP.div`
  position: absolute;
  bottom: 60px;
  width: 198px;
`,tY=(0,U.ZP)(()=>(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsxs)("g",{id:"g1",children:[(0,c.jsx)("path",{id:"path1",fill:"#000000",stroke:"none",d:"M 18.09091 6.883101 L 5.409091 6.883101 L 5.409091 16.746737 L 10.664648 16.746737 C 10.927091 17.116341 11.30353 17.422749 11.792977 17.611004 L 12.664289 17.946156 L 12.744959 18.155828 L 5.409091 18.155828 C 4.630871 18.155828 4 17.524979 4 16.746737 L 4 6.883101 C 4 6.104881 4.630871 5.47401 5.409091 5.47401 L 18.09091 5.47401 C 18.86915 5.47401 19.5 6.104881 19.5 6.883101 L 19.5 12.52348 C 19.247208 11.883823 18.730145 11.365912 18.09091 11.111994 L 18.09091 6.883101 Z M 18.09091 18.155828 L 17.881165 18.155828 L 19.469212 14.368896 C 19.479921 14.343321 19.490206 14.317817 19.5 14.292241 L 19.5 16.746737 C 19.5 17.524979 18.86915 18.155828 18.09091 18.155828 Z"}),(0,c.jsx)("path",{id:"path2",fill:"#000000",fillRule:"evenodd",stroke:"none",d:"M 18.494614 13.960189 C 18.982441 12.796985 17.813459 11.628003 16.650255 12.11576 L 12.133272 14.01 C 10.962248 14.501069 10.987188 16.168798 12.172375 16.62464 L 13.482055 17.128389 L 13.985805 18.438068 C 14.441646 19.623184 16.109375 19.648125 16.600443 18.477171 L 18.494614 13.960189 Z M 17.19515 13.415224 L 15.30098 17.932205 L 14.79723 16.622526 C 14.654066 16.250385 14.359989 15.956307 13.987918 15.813213 L 12.678168 15.309464 L 17.19515 13.415224 Z"})]})}))`
  color: ${e=>e.visible?void 0:"#060709cc"};
`,tX=()=>{let e=(0,p.erw)(),t=(0,p.Dcz)(),[o,n]=(0,x.useState)(!1);return(0,c.jsx)(D.Lt,{position:"top",trigger:"custom",visible:o,onClickOutSide:()=>n(!1),render:(0,c.jsxs)(D.Lt.Menu,{children:[(0,c.jsx)(D.Lt.Item,{onClick:()=>e.zoomin(),children:"Zoom in"}),(0,c.jsx)(D.Lt.Item,{onClick:()=>e.zoomout(),children:"Zoom out"}),(0,c.jsx)(D.iz,{layout:"horizontal"}),(0,c.jsx)(D.Lt.Item,{onClick:()=>t.config.updateZoom(.5),children:"Zoom to 50%"}),(0,c.jsx)(D.Lt.Item,{onClick:()=>t.config.updateZoom(1),children:"Zoom to 100%"}),(0,c.jsx)(D.Lt.Item,{onClick:()=>t.config.updateZoom(1.5),children:"Zoom to 150%"}),(0,c.jsx)(D.Lt.Item,{onClick:()=>t.config.updateZoom(2),children:"Zoom to 200%"})]}),children:(0,c.jsxs)(tH,{onClick:()=>n(!0),children:[Math.floor(100*e.zoom),"%"]})})},tW=(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("path",{id:"switch-line",fill:"currentColor",stroke:"none",d:"M 12.728118 10.060962 C 13.064282 8.716098 14.272528 7.772551 15.65877 7.772343 L 17.689898 7.772343 C 18.0798 7.772343 18.39588 7.456264 18.39588 7.066362 C 18.39588 6.676458 18.0798 6.36038 17.689898 6.36038 L 15.659616 6.36038 C 13.62515 6.360315 11.851767 7.745007 11.358504 9.718771 C 11.02234 11.063635 9.814095 12.007183 8.427853 12.007389 L 7.101437 12.007389 C 6.711768 12.007389 6.395878 12.323277 6.395878 12.712947 C 6.395878 13.102616 6.711768 13.418506 7.101437 13.418506 L 8.426159 13.418506 C 9.812716 13.418323 11.021417 14.361954 11.357657 15.707124 C 11.850921 17.680887 13.624304 19.065578 15.65877 19.065516 L 17.689049 19.065516 C 18.078953 19.065516 18.395033 18.749435 18.395033 18.359533 C 18.395033 17.969631 18.078953 17.653551 17.689049 17.653551 L 15.65877 17.653551 C 14.272528 17.653345 13.064282 16.709797 12.728118 15.364932 C 12.454905 14.27114 11.774856 13.322707 10.826583 12.712947 C 11.774536 12.10303 12.454268 11.154617 12.727271 10.060962 Z"})}),tJ=()=>{let e=(0,p.G2Z)(p.dmf),t=(0,x.useCallback)(()=>{e.switchLineType()},[e]);return(0,c.jsx)(D.u,{content:"Switch Line",children:(0,c.jsx)(D.hU,{type:"tertiary",theme:"borderless",onClick:t,icon:tW})})};function t$(e){let[t,o]=(0,x.useState)(0),n=(0,p.sX$)(),i=(0,x.useCallback)(()=>{o(n.document.getAllNodes().map(e=>(0,p.CMu)(e)).filter(e=>null==e?void 0:e.state.invalid).length)},[n]),r=(0,x.useCallback)(async()=>{let e=n.document.getAllNodes().map(e=>(0,p.CMu)(e));await Promise.all(e.map(async e=>null==e?void 0:e.validate())),console.log(">>>>> save data: ",n.document.toJSON())},[n]);return((0,x.useEffect)(()=>{let e=e=>{let t=(0,p.CMu)(e);if(t){let o=t.onValidate(()=>i());e.onDispose(()=>o.dispose())}};n.document.getAllNodes().map(t=>e(t));let t=n.document.onNodeCreate(t=>{let{node:o}=t;return e(o)});return()=>t.dispose()},[n]),0===t)?(0,c.jsx)(D.zx,{disabled:e.disabled,onClick:r,style:{backgroundColor:"rgba(171,181,255,0.3)",borderRadius:"8px"},children:"Save"}):(0,c.jsx)(D.Ct,{count:t,position:"rightTop",type:"danger",children:(0,c.jsx)(D.zx,{type:"danger",disabled:e.disabled,onClick:r,style:{backgroundColor:"rgba(255, 179, 171, 0.3)",borderRadius:"8px"},children:"\xa0 Save"})})}function tQ(){let[e,t]=(0,x.useState)(!1),o=(0,p.G2Z)(_),n=async()=>{t(!0),await o.startRun(),t(!1)};return(0,c.jsx)(D.zx,{onClick:n,loading:e,style:{backgroundColor:"rgba(171,181,255,0.3)",borderRadius:"8px"},children:"Run"})}var tq=o(7236),tK=o(2549);let t0=()=>{let e=(0,p.Dcz)(),t=(0,x.useCallback)(()=>{e.config.readonly=!e.config.readonly},[e]);return e.config.readonly?(0,c.jsx)(D.u,{content:"Editable",children:(0,c.jsx)(D.hU,{theme:"borderless",type:"tertiary",icon:(0,c.jsx)(tq.Z,{size:"default"}),onClick:t})}):(0,c.jsx)(D.u,{content:"Readonly",children:(0,c.jsx)(D.hU,{theme:"borderless",type:"tertiary",icon:(0,c.jsx)(tK.Z,{size:"default"}),onClick:t})})},t1=e=>{let{minimapVisible:t,setMinimapVisible:o}=e;return(0,c.jsx)(D.u,{content:"Minimap",children:(0,c.jsx)(D.hU,{type:"tertiary",theme:"borderless",icon:(0,c.jsx)(tY,{visible:t}),onClick:()=>o(!t)})})},t2=e=>{let{visible:t}=e,o=(0,p.G2Z)(f.I5);return t?(0,c.jsx)(tG,{children:(0,c.jsx)(f.Zd,{service:o,panelStyles:{},containerStyles:{pointerEvents:"auto",position:"relative",top:"unset",right:"unset",bottom:"unset",left:"unset"},inactiveStyle:{opacity:1,scale:1,translateX:0,translateY:0}})}):(0,c.jsx)(c.Fragment,{})};function t3(e){let{width:t,height:o}=e;return(0,c.jsxs)("svg",{width:t||48,height:o||38,viewBox:"0 0 48 38",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,c.jsx)("rect",{x:"1.83317",y:"1.49998",width:"44.3333",height:"35",rx:"3.5",stroke:"currentColor",strokeOpacity:"0.8",strokeWidth:"2.33333"}),(0,c.jsx)("path",{d:"M14.6665 30.6667H33.3332",stroke:"currentColor",strokeOpacity:"0.8",strokeWidth:"2.33333",strokeLinecap:"round"})]})}let t5=()=>(0,c.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:[(0,c.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.8549 5H3.1451C3.06496 5 3 5.06496 3 5.1451V18.8549C3 18.935 3.06496 19 3.1451 19H20.8549C20.935 19 21 18.935 21 18.8549V5.1451C21 5.06496 20.935 5 20.8549 5ZM3.1451 3C1.96039 3 1 3.96039 1 5.1451V18.8549C1 20.0396 1.96039 21 3.1451 21H20.8549C22.0396 21 23 20.0396 23 18.8549V5.1451C23 3.96039 22.0396 3 20.8549 3H3.1451Z"}),(0,c.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.99991 16C6.99991 15.4477 7.44762 15 7.99991 15H15.9999C16.5522 15 16.9999 15.4477 16.9999 16C16.9999 16.5523 16.5522 17 15.9999 17H7.99991C7.44762 17 6.99991 16.5523 6.99991 16Z"})]});function t9(e){let{width:t,height:o}=e;return(0,c.jsx)("svg",{width:t||34,height:o||52,viewBox:"0 0 34 52",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.9998 16.6666V35.3333C30.9998 37.5748 30.9948 38.4695 30.9 39.1895C30.2108 44.4247 26.0912 48.5443 20.856 49.2335C20.1361 49.3283 19.2413 49.3333 16.9998 49.3333C14.7584 49.3333 13.8636 49.3283 13.1437 49.2335C7.90847 48.5443 3.78888 44.4247 3.09965 39.1895C3.00487 38.4695 2.99984 37.5748 2.99984 35.3333V16.6666C2.99984 14.4252 3.00487 13.5304 3.09965 12.8105C3.78888 7.57528 7.90847 3.45569 13.1437 2.76646C13.7232 2.69017 14.4159 2.67202 15.8332 2.66785V9.86573C14.4738 10.3462 13.4998 11.6426 13.4998 13.1666V17.8332C13.4998 19.3571 14.4738 20.6536 15.8332 21.1341V23.6666C15.8332 24.3109 16.3555 24.8333 16.9998 24.8333C17.6442 24.8333 18.1665 24.3109 18.1665 23.6666V21.1341C19.5259 20.6536 20.4998 19.3572 20.4998 17.8332V13.1666C20.4998 11.6426 19.5259 10.3462 18.1665 9.86571V2.66785C19.5837 2.67202 20.2765 2.69017 20.856 2.76646C26.0912 3.45569 30.2108 7.57528 30.9 12.8105C30.9948 13.5304 30.9998 14.4252 30.9998 16.6666ZM0.666504 16.6666C0.666504 14.4993 0.666504 13.4157 0.786276 12.5059C1.61335 6.22368 6.55687 1.28016 12.8391 0.453085C13.7489 0.333313 14.8325 0.333313 16.9998 0.333313C19.1671 0.333313 20.2508 0.333313 21.1605 0.453085C27.4428 1.28016 32.3863 6.22368 33.2134 12.5059C33.3332 13.4157 33.3332 14.4994 33.3332 16.6666V35.3333C33.3332 37.5006 33.3332 38.5843 33.2134 39.494C32.3863 45.7763 27.4428 50.7198 21.1605 51.5469C20.2508 51.6666 19.1671 51.6666 16.9998 51.6666C14.8325 51.6666 13.7489 51.6666 12.8391 51.5469C6.55687 50.7198 1.61335 45.7763 0.786276 39.494C0.666504 38.5843 0.666504 37.5006 0.666504 35.3333V16.6666ZM15.8332 13.1666C15.8332 13.0011 15.8676 12.8437 15.9297 12.7011C15.9886 12.566 16.0722 12.4443 16.1749 12.3416C16.386 12.1305 16.6777 11.9999 16.9998 11.9999C17.6435 11.9999 18.1654 12.5212 18.1665 13.1646L18.1665 13.1666V17.8332L18.1665 17.8353C18.1665 17.8364 18.1665 17.8376 18.1665 17.8387C18.1661 17.9132 18.1588 17.986 18.1452 18.0565C18.0853 18.3656 17.9033 18.6312 17.6515 18.8011C17.4655 18.9266 17.2412 18.9999 16.9998 18.9999C16.3555 18.9999 15.8332 18.4776 15.8332 17.8332V13.1666Z",fill:"currentColor",fillOpacity:"0.8"})})}let t4=()=>(0,c.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.5 8C4.5 4.13401 7.63401 1 11.5 1H12.5C16.366 1 19.5 4.13401 19.5 8V17C19.5 20.3137 16.8137 23 13.5 23H10.5C7.18629 23 4.5 20.3137 4.5 17V8ZM11.2517 3.00606C8.60561 3.13547 6.5 5.32184 6.5 8V17C6.5 19.2091 8.29086 21 10.5 21H13.5C15.7091 21 17.5 19.2091 17.5 17V8C17.5 5.32297 15.3962 3.13732 12.7517 3.00622V5.28013C13.2606 5.54331 13.6074 6.06549 13.6074 6.66669V8.75759C13.6074 9.35879 13.2606 9.88097 12.7517 10.1441V11.4091C12.7517 11.8233 12.4159 12.1591 12.0017 12.1591C11.5875 12.1591 11.2517 11.8233 11.2517 11.4091V10.1457C10.7411 9.88298 10.3931 9.35994 10.3931 8.75759V6.66669C10.3931 6.06433 10.7411 5.5413 11.2517 5.27862V3.00606ZM12.0017 6.14397C11.7059 6.14397 11.466 6.38381 11.466 6.67968V8.74462C11.466 9.03907 11.7036 9.27804 11.9975 9.28031L12.0002 9.28032C12.0456 9.28032 12.0896 9.27482 12.1316 9.26447C12.3401 9.21256 12.5002 9.0386 12.5318 8.82287C12.5345 8.80149 12.5359 8.7797 12.5359 8.75759V6.66669C12.5359 6.64463 12.5345 6.62288 12.5318 6.60154C12.4999 6.38354 12.3368 6.20817 12.1252 6.15826C12.0856 6.14891 12.0442 6.14397 12.0017 6.14397Z"})}),{Title:t6,Paragraph:t8}=D.Sx,t7=e=>{let{title:t,subTitle:o,icon:n,onChange:i,value:r,selected:s}=e;return(0,c.jsxs)("div",{className:`mouse-pad-option ${s?"mouse-pad-option-selected":""}`,onClick:()=>i(r),children:[(0,c.jsx)("div",{className:`mouse-pad-option-icon ${s?"mouse-pad-option-icon-selected":""}`,children:n}),(0,c.jsx)(t6,{heading:6,className:`mouse-pad-option-title ${s?"mouse-pad-option-title-selected":""}`,children:t}),(0,c.jsx)(t8,{type:"tertiary",className:`mouse-pad-option-subTitle ${s?"mouse-pad-option-subTitle-selected":""}`,children:o})]})},oe=e=>{let{value:t,onChange:o,onPopupVisibleChange:n,containerStyle:i,iconStyle:r,arrowStyle:s}=e,[l,a]=(0,x.useState)(!1);return(0,c.jsx)(D.J2,{trigger:"custom",position:"topLeft",closeOnEsc:!0,visible:l,onVisibleChange:e=>{null==n||n(e)},onClickOutSide:()=>{a(!1)},spacing:20,content:(0,c.jsxs)("div",{className:"ui-mouse-pad-selector-popover",children:[(0,c.jsx)(D.Sx.Title,{heading:4,children:"Interaction mode"}),(0,c.jsxs)("div",{className:"ui-mouse-pad-selector-popover-options",children:[(0,c.jsx)(t7,{title:"Mouse-Friendly",subTitle:"Drag the canvas with the left mouse button, zoom with the scroll wheel.",value:"MOUSE",selected:"MOUSE"===t,icon:(0,c.jsx)(t9,{}),onChange:o}),(0,c.jsx)(t7,{title:"Touchpad-Friendly",subTitle:"Drag with two fingers moving in the same direction, zoom by pinching or spreading two fingers.",value:"PAD",selected:"PAD"===t,icon:(0,c.jsx)(t3,{}),onChange:o})]})]}),children:(0,c.jsx)("div",{className:`ui-mouse-pad-selector ${l?"ui-mouse-pad-selector-active":""}`,onClick:()=>{a(!l)},style:i,children:(0,c.jsx)("div",{className:"ui-mouse-pad-selector-icon",style:r,children:"MOUSE"===t?(0,c.jsx)(t4,{}):(0,c.jsx)(t5,{})})})})},ot="workflow_prefer_interactive_type",oo=/(Macintosh|MacIntel|MacPPC|Mac68K|iPad)/.test(navigator.userAgent),on=()=>{let e=localStorage.getItem(ot);return e&&["MOUSE","PAD"].includes(e)?e:oo?"PAD":"MOUSE"},oi=e=>{localStorage.setItem(ot,e)},or=()=>{let e=(0,p.erw)(),[t,o]=(0,x.useState)(!1),[n,i]=(0,x.useState)(()=>on()),[r,s]=(0,x.useState)(!1);return(0,x.useEffect)(()=>{e.setMouseScrollDelta(e=>e/20);let t=on();e.setInteractiveType(t)},[]),(0,c.jsx)(D.J2,{trigger:"custom",position:"top",visible:t,onClickOutSide:()=>{o(!1)},children:(0,c.jsx)(D.u,{content:"MOUSE"===n?"Mouse-Friendly":"Touchpad-Friendly",style:{display:r?"none":"block"},children:(0,c.jsx)("div",{className:"workflow-toolbar-interactive",children:(0,c.jsx)(oe,{value:n,onChange:t=>{i(t),oi(t),e.setInteractiveType(t)},onPopupVisibleChange:s,containerStyle:{border:"none",height:"32px",width:"32px",justifyContent:"center",alignItems:"center",gap:"2px",padding:"4px",borderRadius:"var(--small, 6px)"},iconStyle:{margin:"0",width:"16px",height:"16px"},arrowStyle:{width:"12px",height:"12px"}})})})})},os=()=>{let e=(0,p.erw)();return(0,c.jsx)(D.u,{content:"FitView",children:(0,c.jsx)(D.hU,{icon:(0,c.jsx)(eb.Z,{}),type:"tertiary",theme:"borderless",onClick:()=>e.fitView()})})},ol=e=>{let{style:t}=e;return(0,c.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",style:t,children:[(0,c.jsx)("path",{d:"M6.5 9C5.94772 9 5.5 9.44772 5.5 10V11C5.5 11.5523 5.94772 12 6.5 12H7.5C8.05228 12 8.5 11.5523 8.5 11V10C8.5 9.44772 8.05228 9 7.5 9H6.5zM11.5 9C10.9477 9 10.5 9.44772 10.5 10V11C10.5 11.5523 10.9477 12 11.5 12H12.5C13.0523 12 13.5 11.5523 13.5 11V10C13.5 9.44772 13.0523 9 12.5 9H11.5zM15.5 10C15.5 9.44772 15.9477 9 16.5 9H17.5C18.0523 9 18.5 9.44772 18.5 10V11C18.5 11.5523 18.0523 12 17.5 12H16.5C15.9477 12 15.5 11.5523 15.5 11V10z"}),(0,c.jsx)("path",{d:"M23 4C23 2.9 22.1 2 21 2H3C1.9 2 1 2.9 1 4V17.0111C1 18.0211 1.9 19.0111 3 19.0111H7.7586L10.4774 22C10.9822 22.5017 11.3166 22.6311 12 22.7009C12.414 22.707 13.0502 22.5093 13.5 22L16.2414 19.0111H21C22.1 19.0111 23 18.1111 23 17.0111V4ZM3 4H21V17.0111H15.5L12 20.6714L8.5 17.0111H3V4Z"})]})},oa=()=>{let e=(0,p.Dcz)(),t=(0,p.G2Z)(p.L4w),o=(0,p.G2Z)(p.oJU),n=(0,p.G2Z)(p.iwM),[i,r]=(0,x.useState)(!1),s=(0,x.useCallback)(t=>{let o=e.config.getPosFromMouseEvent(t);return{x:o.x,y:o.y-75}},[e]),l=(0,x.useCallback)(async e=>{r(!1);let i=s(e),l=t.createWorkflowNodeByType(ty.Comment,i);await (0,p.gw0)(16),o.selectNode(l),n.startDragSelectedNodes(e)},[o,s,t,n]);return(0,c.jsx)(D.u,{trigger:"custom",visible:i,onVisibleChange:r,content:"Comment",children:(0,c.jsx)(D.hU,{disabled:e.config.readonly,icon:(0,c.jsx)(ol,{style:{width:16,height:16}}),type:"tertiary",theme:"borderless",onClick:l,onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)})})},od=(0,c.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("path",{fill:"currentColor",d:"M3 2C2.44772 2 2 2.44771 2 3V12C2 12.5523 2.44772 13 3 13H10C10.5523 13 11 12.5523 11 12V3C11 2.44772 10.5523 2 10 2H3zM4 11V4H9V11H4zM21 22C21.5523 22 22 21.5523 22 21V12C22 11.4477 21.5523 11 21 11H14C13.4477 11 13 11.4477 13 12V21C13 21.5523 13.4477 22 14 22H21zM20 13V20H15V13H20zM2 16C2 15.4477 2.44772 15 3 15H10C10.5523 15 11 15.4477 11 16V21C11 21.5523 10.5523 22 10 22H3C2.44772 22 2 21.5523 2 21V16zM4 20V17H9V20H4zM21 9C21.5523 9 22 8.55228 22 8V3C22 2.44772 21.5523 2 21 2H14C13.4477 2 13 2.44772 13 3V8C13 8.55228 13.4477 9 14 9H21zM20 4V7H15V4H20z"})}),oc=()=>{let e=(0,p.erw)(),t=(0,p.Dcz)(),o=(0,x.useCallback)(async()=>{await e.autoLayout()},[e]);return(0,c.jsx)(D.u,{content:"Auto Layout",children:(0,c.jsx)(D.hU,{disabled:t.config.readonly,type:"tertiary",theme:"borderless",onClick:o,icon:od})})},ou=()=>{let{history:e,playground:t}=(0,p.sX$)(),[o,n]=(0,x.useState)(!1),[i,r]=(0,x.useState)(!1),[s,l]=(0,x.useState)(!0);(0,x.useEffect)(()=>{let t=e.undoRedoService.onChange(()=>{n(e.canUndo()),r(e.canRedo())});return()=>t.dispose()},[e]);let a=(0,p.JAk)();return(0,x.useEffect)(()=>{let e=t.config.onReadonlyOrDisabledChange(()=>a());return()=>e.dispose()},[t]),(0,c.jsx)(tB,{className:"demo-free-layout-tools",children:(0,c.jsxs)(tU,{children:[(0,c.jsx)(or,{}),(0,c.jsx)(oc,{}),(0,c.jsx)(tJ,{}),(0,c.jsx)(tX,{}),(0,c.jsx)(os,{}),(0,c.jsx)(t1,{minimapVisible:s,setMinimapVisible:l}),(0,c.jsx)(t2,{visible:s}),(0,c.jsx)(t0,{}),(0,c.jsx)(oa,{}),(0,c.jsx)(D.u,{content:"Undo",children:(0,c.jsx)(D.hU,{type:"tertiary",theme:"borderless",icon:(0,c.jsx)(tV.Z,{}),disabled:!o||t.config.readonly,onClick:()=>e.undo()})}),(0,c.jsx)(D.u,{content:"Redo",children:(0,c.jsx)(D.hU,{type:"tertiary",theme:"borderless",icon:(0,c.jsx)(tP.Z,{}),disabled:!i||t.config.readonly,onClick:()=>e.redo()})}),(0,c.jsx)(D.iz,{layout:"vertical",style:{height:"16px"},margin:3}),(0,c.jsx)(t_,{disabled:t.config.readonly}),(0,c.jsx)(D.iz,{layout:"vertical",style:{height:"16px"},margin:3}),(0,c.jsx)(t$,{disabled:t.config.readonly}),(0,c.jsx)(tQ,{})]})})};function op(e){let{children:t}=e,[o,n]=(0,x.useState)();return(0,c.jsx)(eW.Provider,{value:{visible:!!o,nodeRender:o,setNodeRender:n},children:t})}let oh=()=>{var e;let{nodeRender:t,setNodeRender:o}=(0,x.useContext)(eW),{selection:n,playground:i}=(0,p.sX$)(),r=(0,p.JAk)(),s=(0,x.useCallback)(()=>{o(void 0)},[]);(0,x.useEffect)(()=>{let e=i.config.onReadonlyOrDisabledChange(()=>r());return()=>e.dispose()},[i]),(0,x.useEffect)(()=>{let e=n.onSelectionChanged(()=>{0===n.selection.length?s():1===n.selection.length&&n.selection[0]!==(null==t?void 0:t.node)&&s()});return()=>e.dispose()},[n,s]),(0,x.useEffect)(()=>{if(t){let e=t.node.onDispose(()=>{o(void 0)});return()=>e.dispose()}return()=>{}},[t]);let l=(0,x.useMemo)(()=>{if(!t)return!1;let{disableSideBar:e=!1}=t.node.getNodeMeta();return!e},[t]);if(i.config.readonly)return null;let a=t?(0,c.jsx)(p.rQR.Provider,{value:t.node,children:(0,c.jsx)(eX.Provider,{value:t,children:null==(e=t.form)?void 0:e.render()})},t.node.id):null;return(0,c.jsx)(D.Uu,{mask:!1,visible:l,onCancel:s,children:(0,c.jsx)(eJ.Provider,{value:!0,children:a})})};(0,u.createRoot)(document.getElementById("root")).render((0,c.jsx)(()=>{let e=(0,x.useMemo)(()=>({background:!0,readonly:!1,initialData:tO,nodeRegistries:tT,getNodeDefaultRegistry:e=>({type:e,meta:{defaultExpanded:!0},formMeta:ej}),fromNodeJSON:(e,t)=>t,toNodeJSON:(e,t)=>t,lineColor:{hidden:"transparent",default:"#4d53e8",drawing:"#5DD6E3",hovered:"#37d0ff",selected:"#37d0ff",error:"red"},canAddLine:(e,t,o)=>t.node!==o.node,canDeleteLine:(e,t,o,n)=>!0,canDeleteNode:(e,t)=>!0,onDragLineEnd:w,selectBox:{SelectorBoxPopover:eY},materials:{renderDefaultNode:e0,renderNodes:{[ty.Comment]:tg}},nodeEngine:{enable:!0},variableEngine:{enable:!0},history:{enable:!0,enableChangeNode:!0},onContentChange:(0,m.Z)((e,t)=>{console.log("Auto Save: ",t,e.document.toJSON())},1e3),isFlowingLine:(e,t)=>e.get(_).isFlowingLine(t),shortcuts:A,onBind:e=>{let{bind:t}=e;t(R).toSelf().inSingletonScope(),t(_).toSelf().inSingletonScope()},onInit(){console.log("--- Playground init ---")},onAllLayersRendered(e){e.document.fitView(!1),console.log("--- Playground rendered ---")},onDispose(){console.log("---- Playground Dispose ----")},plugins:()=>[(0,j.J2)({renderInsideLine:e3}),(0,f.qP)({disableLayer:!0,canvasStyle:{canvasWidth:182,canvasHeight:102,canvasPadding:50,canvasBackground:"rgba(242, 243, 245, 1)",canvasBorderRadius:10,viewportBackground:"rgba(255, 255, 255, 1)",viewportBorderRadius:4,viewportBorderColor:"rgba(6, 7, 9, 0.10)",viewportBorderWidth:1,viewportBorderDashLength:void 0,nodeColor:"rgba(0, 0, 0, 0.10)",nodeBorderRadius:2,nodeBorderWidth:.145,nodeBorderColor:"rgba(6, 7, 9, 0.10)",overlayColor:"rgba(255, 255, 255, 0.55)"},inactiveDebounceTime:1}),B({}),(0,y.Z)({edgeColor:"#00B2B2",alignColor:"#00B2B2",edgeLineWidth:1,alignLineWidth:1,alignCrossWidth:8}),(0,v.dn)({renderer:te}),(0,b.AU)({}),(0,C.IP)({groupNodeRender:eG})]}),[]);return(0,c.jsx)("div",{className:"doc-free-feature-overview",children:(0,c.jsx)(p.EhS,{...e,children:(0,c.jsxs)(op,{children:[(0,c.jsx)("div",{className:"demo-container",children:(0,c.jsx)(p.R6T,{className:"demo-editor"})}),(0,c.jsx)(ou,{}),(0,c.jsx)(oh,{})]})})})},{}))}},t={};function o(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(r.exports,r,r.exports,o),r.loaded=!0,r.exports}o.m=e,o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),o.nc=void 0,(()=>{var e=[];o.O=(t,n,i,r)=>{if(n){r=r||0;for(var s=e.length;s>0&&e[s-1][2]>r;s--)e[s]=e[s-1];e[s]=[n,i,r];return}for(var l=1/0,s=0;s<e.length;s++){for(var[n,i,r]=e[s],a=!0,d=0;d<n.length;d++)(!1&r||l>=r)&&Object.keys(o.O).every(e=>o.O[e](n[d]))?n.splice(d--,1):(a=!1,r<l&&(l=r));if(a){e.splice(s--,1);var c=i();void 0!==c&&(t=c)}}return t}})(),o.p="/",o.rv=()=>"1.3.12",(()=>{var e={980:0};o.O.j=t=>0===e[t];var t=(t,n)=>{var i,r,[s,l,a]=n,d=0;if(s.some(t=>0!==e[t])){for(i in l)o.o(l,i)&&(o.m[i]=l[i]);if(a)var c=a(o)}for(t&&t(n);d<s.length;d++)r=s[d],o.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return o.O(c)},n=self.webpackChunkfast_connect=self.webpackChunkfast_connect||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),o.ruid="bundler=rspack@1.3.12";var n=o.O(void 0,["361","686"],function(){return o(4091)});n=o.O(n)})();