  # File: jobs/build.yml

parameters:
  name: ''
  pool: ''
  sign: false

jobs:
- job: ${{ parameters.name }}
  pool: ${{ parameters.pool }}
  strategy:
    maxParallel: 3
    matrix:
      node-16:
        node_version: ^16.13.0
      node-14:
        node_version: ^14.18.0
      node-12:
        node_version: ^12.6.0
  steps:
  - task: NodeTool@0
    displayName: " Install Node.js"
    inputs:
      versionSpec: $(node_version)
  - script: git config --global user.email "<EMAIL>"
  - script: git config --global user.name "Example Git User"
  - script: |
      curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm@6
      pnpm config set store-dir $(pnpm_config_cache)
    displayName: "Setup pnpm"
  - script: npm install
  - script: npm run build
  - script: npm test && npm run write-coverage
  - script: bash logo/generate.sh
  - task: PublishTestResults@2
    displayName: 'Publish Test Results'
    inputs:
      testResultsFiles: '**/junit-*.xml'
      testRunTitle: TestRun ${{ parameters.name }} $(node_version)
  - task: PublishCodeCoverageResults@1
    displayName: 'Publish code coverage results'
    inputs:
      codeCoverageTool: 'cobertura'
      summaryFileLocation: '**/coverage/cobertura-coverage.xml'
  - task: PublishBuildArtifacts@1
    displayName: Publish packages
    condition: and(succeeded(), eq(variables['system.pullrequest.isfork'], false))
    inputs:
      pathtoPublish: '$(Build.ArtifactStagingDirectory)'
