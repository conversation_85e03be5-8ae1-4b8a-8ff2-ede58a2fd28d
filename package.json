{"name": "fast-connect", "version": "0.1.40", "description": "", "keywords": [], "license": "MIT", "files": ["src/", ".eslintrc.js", ".giti<PERSON>re", "index.html", "package.json", "rsbuild.config.ts", "tsconfig.json"], "dependencies": {"@douyinfe/semi-icons": "^2.72.3", "@douyinfe/semi-ui": "^2.72.3", "@flowgram.ai/form-materials": "0.2.4", "@flowgram.ai/free-container-plugin": "0.2.4", "@flowgram.ai/free-group-plugin": "0.2.4", "@flowgram.ai/free-layout-editor": "0.2.4", "@flowgram.ai/free-lines-plugin": "0.2.4", "@flowgram.ai/free-node-panel-plugin": "0.2.4", "@flowgram.ai/free-snap-plugin": "0.2.4", "@flowgram.ai/minimap-plugin": "0.2.4", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react": "^18", "react-dom": "^18", "styled-components": "^5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@flowgram.ai/eslint-config": "0.2.4", "@flowgram.ai/ts-config": "0.2.4", "@rsbuild/core": "^1.2.16", "@rsbuild/plugin-less": "^1.1.1", "@rsbuild/plugin-react": "^1.1.1", "@types/lodash-es": "^4.17.12", "@types/node": "^18", "@types/react": "^18", "@types/react-dom": "^18", "@types/styled-components": "^5", "commitizen": "^4.3.1", "cross-env": "~7.0.3", "cz-git": "^1.11.1", "eslint": "^8.54.0", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "lint-staged": "^16.1.0", "rimraf": "^5.0.5", "stylelint": "^16.20.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^38.0.0", "stylelint-config-styled-components": "^0.1.1", "typescript": "^5.8.3"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "scripts": {"dev": "cross-env MODE=app NODE_ENV=development rsbuild dev --open", "start": "cross-env NODE_ENV=development rsbuild dev --open", "build": "cross-env NODE_ENV=production rsbuild build", "build:fast": "cross-env NODE_ENV=production rsbuild build --mode=development", "build:watch": "cross-env NODE_ENV=production rsbuild build --watch", "build:analyze": "cross-env NODE_ENV=production rsbuild build --analyze", "build:preview": "npm run build && rsbuild preview", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "lint:style": "stylelint \"src/**/*.{css,less,scss}\"", "lint:style:fix": "stylelint \"src/**/*.{css,less,scss}\" --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,less,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,less,scss,md}\"", "lint:all": "npm run lint && npm run lint:style", "lint:all:fix": "npm run lint:fix && npm run lint:style:fix && npm run format", "commit": "git-cz", "prepare": "husky"}}